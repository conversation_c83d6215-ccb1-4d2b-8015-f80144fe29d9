# 宠物用品电商网站

基于 Next.js 前端 + WordPress 后端的现代化宠物用品电商平台

## 技术栈

### 前端
- **Next.js 15** - React 全栈框架
- **TypeScript** - 类型安全
- **Tailwind CSS** - 现代化样式框架
- **Shadcn/ui** - 高质量 UI 组件库
- **React Query (TanStack Query)** - 数据获取和状态管理
- **Zustand** - 轻量级状态管理
- **Next-auth** - 身份验证
- **Next-intl** - 国际化支持
- **Stripe & PayPal** - 国际支付集成

### 后端
- **WordPress 6.6+** - Headless CMS
- **WooCommerce** - 电商功能
- **WP GraphQL** - GraphQL API
- **WP REST API** - RESTful API
- **Advanced Custom Fields (ACF)** - 自定义字段
- **Stripe Gateway** - 信用卡支付
- **PayPal Gateway** - PayPal 支付
- **Multi-Currency** - 多货币支持

### 基础设施
- **Docker & Docker Compose** - 容器化部署
- **MySQL 8.0** - 数据库
- **Redis** - 缓存和会话存储
- **Nginx** - 反向代理和负载均衡

## 项目结构

```
pet-ecommerce-store/
├── frontend/                 # Next.js 前端应用
├── backend/                  # WordPress 后端配置
├── docker/                   # Docker 相关配置
├── nginx/                    # Nginx 配置
├── docker-compose.yml        # Docker Compose 配置
├── .env.example             # 环境变量示例
└── README.md
```

## 快速开始

1. 克隆项目并进入目录
```bash
git clone <repository-url>
cd pet-ecommerce-store
```

2. 复制环境变量文件
```bash
cp .env.example .env
```

3. 启动所有服务
```bash
docker-compose up -d
```

4. 访问应用
- 前端: http://localhost:3000
- WordPress 管理后台: http://localhost:8080/wp-admin
- 数据库管理: http://localhost:8081 (phpMyAdmin)

## 开发指南

### 本地开发
```bash
# 启动开发环境
docker-compose -f docker-compose.dev.yml up

# 查看日志
docker-compose logs -f

# 重新构建服务
docker-compose up --build
```

### 数据备份
```bash
# 备份数据库
docker-compose exec mysql mysqldump -u root -p wordpress > backup.sql

# 恢复数据库
docker-compose exec -i mysql mysql -u root -p wordpress < backup.sql
```

## 部署

生产环境部署请参考 `docker-compose.prod.yml` 配置文件。

## 许可证

MIT License
