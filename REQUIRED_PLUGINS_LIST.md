# 🔌 Next.js + WordPress 电商网站必需插件清单

## 🛒 核心电商插件 (必装)

### 1. WooCommerce
- **插件名**: WooCommerce
- **作用**: 电商核心功能，产品管理、订单处理、库存管理
- **安装**: `wp plugin install woocommerce --activate`
- **优先级**: ⭐⭐⭐⭐⭐ (必须)

### 2. WooCommerce Stripe Gateway
- **插件名**: WooCommerce Stripe Gateway
- **作用**: 信用卡支付处理 (Visa, Mastercard, Amex, Apple Pay, Google Pay)
- **安装**: `wp plugin install woocommerce-gateway-stripe --activate`
- **优先级**: ⭐⭐⭐⭐⭐ (必须)

### 3. PayPal for WooCommerce
- **插件名**: PayPal for WooCommerce
- **作用**: PayPal 支付集成
- **安装**: `wp plugin install woocommerce-paypal-payments --activate`
- **优先级**: ⭐⭐⭐⭐⭐ (必须)

## 🔗 API 和前端集成插件 (必装)

### 4. WP GraphQL
- **插件名**: WPGraphQL
- **作用**: 提供 GraphQL API，Next.js 前端数据获取
- **安装**: `wp plugin install wp-graphql --activate`
- **优先级**: ⭐⭐⭐⭐⭐ (必须)

### 5. WPGraphQL for WooCommerce
- **插件名**: WooGraphQL
- **作用**: WooCommerce 数据的 GraphQL 查询支持
- **安装**: `wp plugin install wp-graphql-woocommerce --activate`
- **优先级**: ⭐⭐⭐⭐⭐ (必须)

### 6. WP CORS
- **插件名**: WP CORS
- **作用**: 跨域请求支持，允许 Next.js 前端访问 WordPress API
- **安装**: `wp plugin install wp-cors --activate`
- **优先级**: ⭐⭐⭐⭐⭐ (必须)

### 7. JWT Authentication for WP REST API
- **插件名**: JWT Authentication for WP REST API
- **作用**: JWT 令牌认证，用户登录和会话管理
- **安装**: `wp plugin install jwt-authentication-for-wp-rest-api --activate`
- **优先级**: ⭐⭐⭐⭐⭐ (必须)

## 🌍 国际化插件 (强烈推荐)

### 8. WPML Multilingual CMS
- **插件名**: WPML
- **作用**: 多语言支持 (英、法、德、西、意、日、韩)
- **安装**: 需要购买许可证，手动安装
- **优先级**: ⭐⭐⭐⭐ (强烈推荐)

### 9. WooCommerce Multilingual
- **插件名**: WooCommerce Multilingual & Multi-Currency
- **作用**: WooCommerce 多语言和多货币支持
- **安装**: WPML 扩展，需要许可证
- **优先级**: ⭐⭐⭐⭐ (强烈推荐)

### 10. Currency Switcher for WooCommerce
- **插件名**: WOOCS - WooCommerce Currency Switcher
- **作用**: 多货币切换器 (USD, EUR, GBP, CAD, AUD, JPY, KRW)
- **安装**: `wp plugin install woocommerce-currency-switcher --activate`
- **优先级**: ⭐⭐⭐⭐ (强烈推荐)

## 🎨 内容管理插件 (推荐)

### 11. Advanced Custom Fields
- **插件名**: Advanced Custom Fields (ACF)
- **作用**: 自定义字段，产品额外信息、SEO 数据
- **安装**: `wp plugin install advanced-custom-fields --activate`
- **优先级**: ⭐⭐⭐⭐ (推荐)

### 12. ACF to WP-API
- **插件名**: ACF to REST API
- **作用**: 将 ACF 字段暴露给 REST API
- **安装**: `wp plugin install acf-to-rest-api --activate`
- **优先级**: ⭐⭐⭐ (推荐)

## ⚡ 性能优化插件 (推荐)

### 13. Redis Object Cache
- **插件名**: Redis Object Cache
- **作用**: Redis 缓存集成，提高数据库查询性能
- **安装**: `wp plugin install redis-cache --activate`
- **优先级**: ⭐⭐⭐⭐ (推荐)

### 14. WP Rocket
- **插件名**: WP Rocket
- **作用**: 页面缓存、文件压缩、CDN 集成
- **安装**: 需要购买许可证
- **优先级**: ⭐⭐⭐ (可选)

### 15. Smush
- **插件名**: Smush
- **作用**: 图片压缩和优化
- **安装**: `wp plugin install wp-smushit --activate`
- **优先级**: ⭐⭐⭐ (推荐)

## 🔒 安全和 SEO 插件 (推荐)

### 16. Yoast SEO
- **插件名**: Yoast SEO
- **作用**: SEO 优化、XML 站点地图、社交媒体集成
- **安装**: `wp plugin install wordpress-seo --activate`
- **优先级**: ⭐⭐⭐⭐ (推荐)

### 17. Wordfence Security
- **插件名**: Wordfence Security
- **作用**: 安全防护、恶意软件扫描、防火墙
- **安装**: `wp plugin install wordfence --activate`
- **优先级**: ⭐⭐⭐ (推荐)

## 📊 分析和营销插件 (可选)

### 18. Google Analytics for WordPress
- **插件名**: MonsterInsights
- **作用**: Google Analytics 集成
- **安装**: `wp plugin install google-analytics-for-wordpress --activate`
- **优先级**: ⭐⭐⭐ (可选)

### 19. MailChimp for WooCommerce
- **插件名**: Mailchimp for WooCommerce
- **作用**: 邮件营销集成
- **安装**: `wp plugin install mailchimp-for-woocommerce --activate`
- **优先级**: ⭐⭐ (可选)

## 🛠️ 开发和调试插件 (开发环境)

### 20. Query Monitor
- **插件名**: Query Monitor
- **作用**: 数据库查询调试、性能监控
- **安装**: `wp plugin install query-monitor --activate`
- **优先级**: ⭐⭐⭐ (开发环境)

### 21. WP GraphiQL
- **插件名**: WPGraphiQL
- **作用**: GraphQL 查询调试界面
- **安装**: `wp plugin install wp-graphiql --activate`
- **优先级**: ⭐⭐⭐ (开发环境)

## 📦 库存和物流插件 (可选)

### 22. WooCommerce PDF Invoices & Packing Slips
- **插件名**: WooCommerce PDF Invoices & Packing Slips
- **作用**: 自动生成发票和装箱单
- **安装**: `wp plugin install woocommerce-pdf-invoices-packing-slips --activate`
- **优先级**: ⭐⭐ (可选)

### 23. WooCommerce Shipment Tracking
- **插件名**: WooCommerce Shipment Tracking
- **作用**: 订单跟踪功能
- **安装**: `wp plugin install woocommerce-shipment-tracking --activate`
- **优先级**: ⭐⭐ (可选)

## 🎯 宠物用品专用插件 (可选)

### 24. WooCommerce Product Add-Ons
- **插件名**: WooCommerce Product Add-Ons
- **作用**: 产品个性化选项 (宠物名字刻字等)
- **安装**: 需要购买 WooCommerce 扩展
- **优先级**: ⭐⭐ (可选)

### 25. WooCommerce Subscriptions
- **插件名**: WooCommerce Subscriptions
- **作用**: 订阅服务 (宠物食品定期配送)
- **安装**: 需要购买 WooCommerce 扩展
- **优先级**: ⭐⭐ (可选)

---

## 🚀 快速安装脚本

### 核心必需插件 (一键安装)
```bash
# 进入 WordPress 容器
docker exec -it pet-store-wordpress bash

# 安装核心电商插件
wp plugin install woocommerce --activate --allow-root
wp plugin install woocommerce-gateway-stripe --activate --allow-root
wp plugin install woocommerce-paypal-payments --activate --allow-root

# 安装 API 集成插件
wp plugin install wp-graphql --activate --allow-root
wp plugin install wp-graphql-woocommerce --activate --allow-root
wp plugin install wp-cors --activate --allow-root
wp plugin install jwt-authentication-for-wp-rest-api --activate --allow-root

# 安装内容管理插件
wp plugin install advanced-custom-fields --activate --allow-root
wp plugin install acf-to-rest-api --activate --allow-root

# 安装性能优化插件
wp plugin install redis-cache --activate --allow-root
wp plugin install wp-smushit --activate --allow-root

# 安装 SEO 插件
wp plugin install wordpress-seo --activate --allow-root

# 启用 Redis 缓存
wp redis enable --allow-root
```

### 国际化插件 (需要许可证)
```bash
# 多货币支持
wp plugin install woocommerce-currency-switcher --activate --allow-root

# WPML 需要手动安装许可证版本
```

### 开发环境插件
```bash
# 调试和监控
wp plugin install query-monitor --activate --allow-root
wp plugin install wp-graphiql --activate --allow-root
```

## 📋 安装优先级

### 第一阶段 (立即安装)
1. WooCommerce
2. WP GraphQL
3. WPGraphQL for WooCommerce
4. WP CORS
5. JWT Authentication

### 第二阶段 (基础功能)
6. Stripe Gateway
7. PayPal Payments
8. Advanced Custom Fields
9. Redis Object Cache
10. Yoast SEO

### 第三阶段 (国际化)
11. WPML (需要许可证)
12. Currency Switcher
13. WooCommerce Multilingual

### 第四阶段 (优化和扩展)
14. 其他性能和营销插件

---

**总计**: 25 个插件
- **必需**: 7 个
- **强烈推荐**: 8 个  
- **推荐**: 6 个
- **可选**: 4 个
