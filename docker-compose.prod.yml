version: '3.8'

services:
  # MySQL 数据库 (生产环境)
  mysql:
    image: mysql:8.0
    container_name: pet-store-mysql-prod
    restart: always
    environment:
      MYSQL_ROOT_PASSWORD: ${MYSQL_ROOT_PASSWORD}
      MYSQL_DATABASE: ${MYSQL_DATABASE}
      MYSQL_USER: ${MYSQL_USER}
      MYSQL_PASSWORD: ${MYSQL_PASSWORD}
    volumes:
      - mysql_data_prod:/var/lib/mysql
      - ./docker/mysql/prod-init:/docker-entrypoint-initdb.d
    networks:
      - pet-store-network
    command: --default-authentication-plugin=mysql_native_password --innodb-buffer-pool-size=1G
    deploy:
      resources:
        limits:
          memory: 2G
        reservations:
          memory: 1G

  # Redis 缓存 (生产环境)
  redis:
    image: redis:7-alpine
    container_name: pet-store-redis-prod
    restart: always
    volumes:
      - redis_data_prod:/data
      - ./docker/redis/redis.conf:/usr/local/etc/redis/redis.conf
    networks:
      - pet-store-network
    command: redis-server /usr/local/etc/redis/redis.conf
    deploy:
      resources:
        limits:
          memory: 512M
        reservations:
          memory: 256M

  # WordPress 后端 (生产环境)
  wordpress:
    build:
      context: ./backend
      dockerfile: Dockerfile.prod
    container_name: pet-store-wordpress-prod
    restart: always
    environment:
      WORDPRESS_DB_HOST: mysql:3306
      WORDPRESS_DB_NAME: ${MYSQL_DATABASE}
      WORDPRESS_DB_USER: ${MYSQL_USER}
      WORDPRESS_DB_PASSWORD: ${MYSQL_PASSWORD}
      WORDPRESS_TABLE_PREFIX: wp_
      WORDPRESS_DEBUG: false
      WP_REDIS_HOST: redis
      WP_REDIS_PORT: 6379
      WP_CACHE: true
    volumes:
      - wordpress_data_prod:/var/www/html
      - ./backend/themes:/var/www/html/wp-content/themes/custom
      - ./backend/plugins:/var/www/html/wp-content/plugins/custom
      - ./backend/uploads:/var/www/html/wp-content/uploads
    depends_on:
      - mysql
      - redis
    networks:
      - pet-store-network
    deploy:
      resources:
        limits:
          memory: 1G
        reservations:
          memory: 512M

  # Next.js 前端 (生产环境)
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
      target: production
    container_name: pet-store-frontend-prod
    restart: always
    environment:
      NODE_ENV: production
      NEXT_PUBLIC_WORDPRESS_URL: ${NEXT_PUBLIC_WORDPRESS_URL}
      NEXT_PUBLIC_API_URL: ${NEXT_PUBLIC_API_URL}
      NEXTAUTH_SECRET: ${NEXTAUTH_SECRET}
      NEXTAUTH_URL: ${NEXTAUTH_URL}
    depends_on:
      - wordpress
    networks:
      - pet-store-network
    deploy:
      resources:
        limits:
          memory: 1G
        reservations:
          memory: 512M

  # Nginx 反向代理 (生产环境)
  nginx:
    image: nginx:alpine
    container_name: pet-store-nginx-prod
    restart: always
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.prod.conf:/etc/nginx/nginx.conf
      - ./nginx/conf.d:/etc/nginx/conf.d
      - ./nginx/ssl:/etc/nginx/ssl
      - ./nginx/logs:/var/log/nginx
    depends_on:
      - frontend
      - wordpress
    networks:
      - pet-store-network
    deploy:
      resources:
        limits:
          memory: 256M
        reservations:
          memory: 128M

volumes:
  mysql_data_prod:
  redis_data_prod:
  wordpress_data_prod:

networks:
  pet-store-network:
    driver: bridge
