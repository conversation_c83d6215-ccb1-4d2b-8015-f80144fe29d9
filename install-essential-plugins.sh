#!/bin/bash

# Next.js + WordPress 电商网站 - 7个核心必需插件安装脚本

echo "🔌 开始安装 7 个核心必需插件..."
echo ""

# 进入 WordPress 容器并安装插件
docker exec -it pet-store-wordpress bash -c "
cd /var/www/html

echo '=== 安装 7 个核心必需插件 ==='
echo ''

echo '1/7 安装 WooCommerce (电商核心功能)...'
wp plugin install woocommerce --activate --allow-root
if [ \$? -eq 0 ]; then
    echo '✅ WooCommerce 安装成功'
else
    echo '❌ WooCommerce 安装失败'
    exit 1
fi
echo ''

echo '2/7 安装 WP GraphQL (GraphQL API)...'
wp plugin install wp-graphql --activate --allow-root
if [ \$? -eq 0 ]; then
    echo '✅ WP GraphQL 安装成功'
else
    echo '❌ WP GraphQL 安装失败'
    exit 1
fi
echo ''

echo '3/7 安装 WPGraphQL for WooCommerce (WooCommerce GraphQL 支持)...'
wp plugin install wp-graphql-woocommerce --activate --allow-root
if [ \$? -eq 0 ]; then
    echo '✅ WPGraphQL for WooCommerce 安装成功'
else
    echo '❌ WPGraphQL for WooCommerce 安装失败'
    exit 1
fi
echo ''

echo '4/7 安装 WP CORS (跨域请求支持)...'
wp plugin install wp-cors --activate --allow-root
if [ \$? -eq 0 ]; then
    echo '✅ WP CORS 安装成功'
else
    echo '❌ WP CORS 安装失败'
    exit 1
fi
echo ''

echo '5/7 安装 JWT Authentication (用户认证)...'
wp plugin install jwt-authentication-for-wp-rest-api --activate --allow-root
if [ \$? -eq 0 ]; then
    echo '✅ JWT Authentication 安装成功'
else
    echo '❌ JWT Authentication 安装失败'
    exit 1
fi
echo ''

echo '6/7 安装 WooCommerce Stripe Gateway (信用卡支付)...'
wp plugin install woocommerce-gateway-stripe --activate --allow-root
if [ \$? -eq 0 ]; then
    echo '✅ Stripe Gateway 安装成功'
else
    echo '❌ Stripe Gateway 安装失败'
    exit 1
fi
echo ''

echo '7/7 安装 PayPal for WooCommerce (PayPal 支付)...'
wp plugin install woocommerce-paypal-payments --activate --allow-root
if [ \$? -eq 0 ]; then
    echo '✅ PayPal Payments 安装成功'
else
    echo '❌ PayPal Payments 安装失败'
    exit 1
fi
echo ''

echo '=== 配置基础设置 ==='

echo '配置 WooCommerce 基础设置...'
wp option update woocommerce_store_address '123 Pet Street' --allow-root
wp option update woocommerce_store_city 'New York' --allow-root
wp option update woocommerce_default_country 'US:NY' --allow-root
wp option update woocommerce_store_postcode '10001' --allow-root
wp option update woocommerce_currency 'USD' --allow-root
wp option update woocommerce_product_type 'both' --allow-root
wp option update woocommerce_allow_tracking 'no' --allow-root

echo '配置货币设置...'
wp option update woocommerce_currency_pos 'left' --allow-root
wp option update woocommerce_price_thousand_sep ',' --allow-root
wp option update woocommerce_price_decimal_sep '.' --allow-root
wp option update woocommerce_price_num_decimals '2' --allow-root

echo '设置永久链接...'
wp rewrite structure '/%postname%/' --allow-root
wp rewrite flush --allow-root

echo '清理缓存...'
wp cache flush --allow-root

echo ''
echo '=== 安装完成! ==='
echo ''
echo '✅ 已成功安装 7 个核心插件:'
echo '   1. WooCommerce - 电商核心功能'
echo '   2. WP GraphQL - GraphQL API'
echo '   3. WPGraphQL for WooCommerce - WooCommerce GraphQL 支持'
echo '   4. WP CORS - 跨域请求支持'
echo '   5. JWT Authentication - 用户认证'
echo '   6. Stripe Gateway - 信用卡支付'
echo '   7. PayPal Payments - PayPal 支付'
echo ''

echo '当前激活的插件列表:'
wp plugin list --status=active --format=table --allow-root
"

echo ""
echo "🎉 核心插件安装完成!"
echo ""
echo "📱 重要访问地址:"
echo "- WordPress 管理后台: http://localhost:8080/wp-admin"
echo "- WooCommerce 设置: http://localhost:8080/wp-admin/admin.php?page=wc-settings"
echo "- GraphQL 端点: http://localhost:8080/graphql"
echo "- REST API 端点: http://localhost:8080/wp-json/wp/v2/"
echo ""
echo "🔧 下一步操作:"
echo "1. 配置 Stripe 支付设置 (测试模式)"
echo "2. 配置 PayPal 支付设置 (沙盒模式)"
echo "3. 创建产品分类 (狗狗用品、猫咪用品等)"
echo "4. 添加示例产品"
echo "5. 测试 GraphQL 查询"
echo ""
echo "💡 提示: 这 7 个插件提供了完整的电商功能，足以运行一个功能完整的宠物用品电商网站！"
