# 🔌 Next.js + WordPress 电商网站插件总结

## 🎯 核心必需插件 (7个) - 必须安装

| 插件名 | 作用 | 优先级 |
|--------|------|--------|
| **WooCommerce** | 电商核心功能 | ⭐⭐⭐⭐⭐ |
| **WP GraphQL** | GraphQL API (Next.js 数据获取) | ⭐⭐⭐⭐⭐ |
| **WPGraphQL for WooCommerce** | WooCommerce GraphQL 支持 | ⭐⭐⭐⭐⭐ |
| **WP CORS** | 跨域请求支持 | ⭐⭐⭐⭐⭐ |
| **JWT Authentication** | 用户认证和会话管理 | ⭐⭐⭐⭐⭐ |
| **Stripe Gateway** | 信用卡支付 (含 Apple Pay, Google Pay) | ⭐⭐⭐⭐⭐ |
| **PayPal Payments** | PayPal 支付集成 | ⭐⭐⭐⭐⭐ |

## 🌍 国际化插件 (3个) - 强烈推荐

| 插件名 | 作用 | 优先级 |
|--------|------|--------|
| **WPML** | 多语言支持 (需要许可证) | ⭐⭐⭐⭐ |
| **WooCommerce Multilingual** | WooCommerce 多语言 (需要许可证) | ⭐⭐⭐⭐ |
| **Currency Switcher** | 多货币切换 | ⭐⭐⭐⭐ |

## 🎨 内容管理插件 (2个) - 推荐

| 插件名 | 作用 | 优先级 |
|--------|------|--------|
| **Advanced Custom Fields** | 自定义字段 | ⭐⭐⭐⭐ |
| **ACF to REST API** | ACF 字段 API 暴露 | ⭐⭐⭐ |

## ⚡ 性能优化插件 (2个) - 推荐

| 插件名 | 作用 | 优先级 |
|--------|------|--------|
| **Redis Object Cache** | Redis 缓存集成 | ⭐⭐⭐⭐ |
| **Smush** | 图片压缩优化 | ⭐⭐⭐ |

## 🔒 SEO 和安全插件 (2个) - 推荐

| 插件名 | 作用 | 优先级 |
|--------|------|--------|
| **Yoast SEO** | SEO 优化 | ⭐⭐⭐⭐ |
| **Wordfence Security** | 安全防护 | ⭐⭐⭐ |

## 🛠️ 开发工具插件 (2个) - 开发环境

| 插件名 | 作用 | 优先级 |
|--------|------|--------|
| **Query Monitor** | 数据库查询调试 | ⭐⭐⭐ |
| **WP GraphiQL** | GraphQL 查询调试界面 | ⭐⭐⭐ |

---

## 🚀 快速安装命令

### 一键安装所有核心插件
```bash
./install-core-plugins.sh
```

### 手动安装核心插件
```bash
# 进入 WordPress 容器
docker exec -it pet-store-wordpress bash

# 安装核心插件
wp plugin install woocommerce --activate --allow-root
wp plugin install wp-graphql --activate --allow-root
wp plugin install wp-graphql-woocommerce --activate --allow-root
wp plugin install wp-cors --activate --allow-root
wp plugin install jwt-authentication-for-wp-rest-api --activate --allow-root
wp plugin install woocommerce-gateway-stripe --activate --allow-root
wp plugin install woocommerce-paypal-payments --activate --allow-root

# 启用 Redis 缓存
wp plugin install redis-cache --activate --allow-root
wp redis enable --allow-root
```

## 📋 安装顺序建议

### 第一批 (立即安装)
1. WooCommerce
2. WP GraphQL
3. WPGraphQL for WooCommerce
4. WP CORS
5. JWT Authentication

### 第二批 (支付功能)
6. Stripe Gateway
7. PayPal Payments

### 第三批 (内容和性能)
8. Advanced Custom Fields
9. Redis Object Cache
10. Yoast SEO

### 第四批 (国际化 - 需要许可证)
11. WPML
12. WooCommerce Multilingual
13. Currency Switcher

## 💰 费用说明

### 免费插件 (13个)
- WooCommerce
- WP GraphQL
- WPGraphQL for WooCommerce
- WP CORS
- JWT Authentication
- Stripe Gateway
- PayPal Payments
- Advanced Custom Fields
- ACF to REST API
- Redis Object Cache
- Smush
- Yoast SEO
- Wordfence Security
- Query Monitor
- WP GraphiQL
- Currency Switcher

### 付费插件 (需要许可证)
- **WPML** - $99/年 (多语言)
- **WooCommerce Multilingual** - 包含在 WPML 中
- **WP Rocket** - $59/年 (可选，缓存优化)
- **WooCommerce Product Add-Ons** - $49/年 (可选)
- **WooCommerce Subscriptions** - $199/年 (可选)

## 🎯 最小可行配置 (MVP)

如果预算有限，最小配置只需要这 7 个免费插件：

1. ✅ WooCommerce
2. ✅ WP GraphQL  
3. ✅ WPGraphQL for WooCommerce
4. ✅ WP CORS
5. ✅ JWT Authentication
6. ✅ Stripe Gateway
7. ✅ PayPal Payments

这个配置就可以运行一个完整的 Next.js + WordPress 电商网站！

## 📞 技术支持

### 插件文档链接
- [WooCommerce 文档](https://docs.woocommerce.com/)
- [WP GraphQL 文档](https://www.wpgraphql.com/docs/)
- [WPML 文档](https://wpml.org/documentation/)
- [Stripe 文档](https://stripe.com/docs)
- [PayPal 开发者文档](https://developer.paypal.com/)

### 常见问题
1. **Q**: 哪些插件是绝对必需的？
   **A**: 前 7 个核心插件是必需的，其他都是增强功能。

2. **Q**: 不安装 WPML 可以吗？
   **A**: 可以，但会失去多语言功能。可以先用英语运行，后续再添加。

3. **Q**: Redis 缓存必须要吗？
   **A**: 不是必需的，但强烈推荐，可以显著提高性能。

---

**总计**: 18 个插件 (13 个免费 + 5 个付费可选)
**最小配置**: 7 个免费核心插件
**推荐配置**: 16 个插件 (13 个免费 + 3 个付费国际化插件)
