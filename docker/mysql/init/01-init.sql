-- MySQL 初始化脚本

-- 创建数据库（如果不存在）
CREATE DATABASE IF NOT EXISTS wordpress CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- 使用数据库
USE wordpress;

-- 设置 MySQL 配置
SET GLOBAL innodb_buffer_pool_size = 1073741824; -- 1GB
SET GLOBAL innodb_log_file_size = 268435456; -- 256MB
SET GLOBAL max_connections = 200;
SET GLOBAL query_cache_size = 67108864; -- 64MB
SET GLOBAL query_cache_type = 1;

-- 创建索引优化表
CREATE TABLE IF NOT EXISTS wp_query_cache (
    cache_key VARCHAR(255) NOT NULL,
    cache_value LONGTEXT,
    cache_expiry DATETIME,
    PRIMARY KEY (cache_key),
    INDEX idx_expiry (cache_expiry)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 创建会话表
CREATE TABLE IF NOT EXISTS wp_sessions (
    session_id VARCHAR(255) NOT NULL,
    session_data LONGTEXT,
    session_expiry DATETIME,
    PRIMARY KEY (session_id),
    INDEX idx_expiry (session_expiry)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 创建日志表
CREATE TABLE IF NOT EXISTS wp_error_logs (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    log_level VARCHAR(20) NOT NULL,
    message TEXT NOT NULL,
    context JSON,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_level (log_level),
    INDEX idx_created (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 创建性能监控表
CREATE TABLE IF NOT EXISTS wp_performance_logs (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    request_uri VARCHAR(500),
    execution_time DECIMAL(10,4),
    memory_usage BIGINT,
    query_count INT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_uri (request_uri(255)),
    INDEX idx_time (execution_time),
    INDEX idx_created (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 优化设置
SET GLOBAL innodb_flush_log_at_trx_commit = 2;
SET GLOBAL sync_binlog = 0;
SET GLOBAL innodb_doublewrite = 0;
