#!/bin/bash

# Next.js + WordPress 电商网站核心插件自动安装脚本

echo "🔌 开始安装 Next.js + WordPress 电商网站核心插件..."

# 进入 WordPress 容器并安装插件
docker exec -it pet-store-wordpress bash -c "
cd /var/www/html

echo '=== 第一阶段: 核心电商插件 ==='

echo '1. 安装 WooCommerce...'
wp plugin install woocommerce --activate --allow-root
if [ \$? -eq 0 ]; then
    echo '✅ WooCommerce 安装成功'
else
    echo '❌ WooCommerce 安装失败'
fi

echo '2. 安装 WooCommerce Stripe Gateway...'
wp plugin install woocommerce-gateway-stripe --activate --allow-root
if [ \$? -eq 0 ]; then
    echo '✅ Stripe Gateway 安装成功'
else
    echo '❌ Stripe Gateway 安装失败'
fi

echo '3. 安装 PayPal for WooCommerce...'
wp plugin install woocommerce-paypal-payments --activate --allow-root
if [ \$? -eq 0 ]; then
    echo '✅ PayPal Payments 安装成功'
else
    echo '❌ PayPal Payments 安装失败'
fi

echo ''
echo '=== 第二阶段: API 集成插件 ==='

echo '4. 安装 WP GraphQL...'
wp plugin install wp-graphql --activate --allow-root
if [ \$? -eq 0 ]; then
    echo '✅ WP GraphQL 安装成功'
else
    echo '❌ WP GraphQL 安装失败'
fi

echo '5. 安装 WPGraphQL for WooCommerce...'
wp plugin install wp-graphql-woocommerce --activate --allow-root
if [ \$? -eq 0 ]; then
    echo '✅ WPGraphQL for WooCommerce 安装成功'
else
    echo '❌ WPGraphQL for WooCommerce 安装失败'
fi

echo '6. 安装 WP CORS...'
wp plugin install wp-cors --activate --allow-root
if [ \$? -eq 0 ]; then
    echo '✅ WP CORS 安装成功'
else
    echo '❌ WP CORS 安装失败'
fi

echo '7. 安装 JWT Authentication...'
wp plugin install jwt-authentication-for-wp-rest-api --activate --allow-root
if [ \$? -eq 0 ]; then
    echo '✅ JWT Authentication 安装成功'
else
    echo '❌ JWT Authentication 安装失败'
fi

echo ''
echo '=== 第三阶段: 内容管理插件 ==='

echo '8. 安装 Advanced Custom Fields...'
wp plugin install advanced-custom-fields --activate --allow-root
if [ \$? -eq 0 ]; then
    echo '✅ Advanced Custom Fields 安装成功'
else
    echo '❌ Advanced Custom Fields 安装失败'
fi

echo '9. 安装 ACF to REST API...'
wp plugin install acf-to-rest-api --activate --allow-root
if [ \$? -eq 0 ]; then
    echo '✅ ACF to REST API 安装成功'
else
    echo '❌ ACF to REST API 安装失败'
fi

echo ''
echo '=== 第四阶段: 性能优化插件 ==='

echo '10. 安装 Redis Object Cache...'
wp plugin install redis-cache --activate --allow-root
if [ \$? -eq 0 ]; then
    echo '✅ Redis Object Cache 安装成功'
    echo '启用 Redis 缓存...'
    wp redis enable --allow-root
    if [ \$? -eq 0 ]; then
        echo '✅ Redis 缓存已启用'
    else
        echo '⚠️  Redis 缓存启用失败，请手动启用'
    fi
else
    echo '❌ Redis Object Cache 安装失败'
fi

echo '11. 安装 Smush 图片优化...'
wp plugin install wp-smushit --activate --allow-root
if [ \$? -eq 0 ]; then
    echo '✅ Smush 安装成功'
else
    echo '❌ Smush 安装失败'
fi

echo ''
echo '=== 第五阶段: SEO 和安全插件 ==='

echo '12. 安装 Yoast SEO...'
wp plugin install wordpress-seo --activate --allow-root
if [ \$? -eq 0 ]; then
    echo '✅ Yoast SEO 安装成功'
else
    echo '❌ Yoast SEO 安装失败'
fi

echo '13. 安装 Wordfence Security...'
wp plugin install wordfence --activate --allow-root
if [ \$? -eq 0 ]; then
    echo '✅ Wordfence Security 安装成功'
else
    echo '❌ Wordfence Security 安装失败'
fi

echo ''
echo '=== 第六阶段: 国际化插件 ==='

echo '14. 安装 Currency Switcher...'
wp plugin install woocommerce-currency-switcher --activate --allow-root
if [ \$? -eq 0 ]; then
    echo '✅ Currency Switcher 安装成功'
else
    echo '❌ Currency Switcher 安装失败'
fi

echo ''
echo '=== 第七阶段: 开发工具插件 ==='

echo '15. 安装 Query Monitor (开发环境)...'
wp plugin install query-monitor --activate --allow-root
if [ \$? -eq 0 ]; then
    echo '✅ Query Monitor 安装成功'
else
    echo '❌ Query Monitor 安装失败'
fi

echo '16. 安装 WP GraphiQL (开发环境)...'
wp plugin install wp-graphiql --activate --allow-root
if [ \$? -eq 0 ]; then
    echo '✅ WP GraphiQL 安装成功'
else
    echo '❌ WP GraphiQL 安装失败'
fi

echo ''
echo '=== 配置基础设置 ==='

echo '配置 WooCommerce 基础设置...'
wp option update woocommerce_store_address '123 Pet Street' --allow-root
wp option update woocommerce_store_city 'New York' --allow-root
wp option update woocommerce_default_country 'US:NY' --allow-root
wp option update woocommerce_store_postcode '10001' --allow-root
wp option update woocommerce_currency 'USD' --allow-root
wp option update woocommerce_product_type 'both' --allow-root
wp option update woocommerce_allow_tracking 'no' --allow-root

echo '配置货币设置...'
wp option update woocommerce_currency_pos 'left' --allow-root
wp option update woocommerce_price_thousand_sep ',' --allow-root
wp option update woocommerce_price_decimal_sep '.' --allow-root
wp option update woocommerce_price_num_decimals '2' --allow-root

echo '设置永久链接...'
wp rewrite structure '/%postname%/' --allow-root
wp rewrite flush --allow-root

echo '清理缓存...'
wp cache flush --allow-root

echo ''
echo '=== 安装完成总结 ==='
echo '✅ 核心插件安装完成!'
echo ''
echo '已安装的插件:'
wp plugin list --status=active --allow-root

echo ''
echo '📋 下一步操作:'
echo '1. 访问 WordPress 管理后台: http://localhost:8080/wp-admin'
echo '2. 配置 Stripe 支付设置'
echo '3. 配置 PayPal 支付设置'
echo '4. 创建产品分类和示例产品'
echo '5. 配置多货币设置'
echo '6. 安装 WPML 多语言插件 (需要许可证)'
echo ''
echo '🔧 手动安装的插件 (需要许可证):'
echo '- WPML Multilingual CMS'
echo '- WooCommerce Multilingual'
echo '- WP Rocket (可选)'
echo '- WooCommerce Product Add-Ons (可选)'
echo '- WooCommerce Subscriptions (可选)'
"

echo ""
echo "🎉 插件安装脚本执行完成!"
echo "请检查上面的输出，确认所有插件都安装成功。"
echo ""
echo "📱 访问地址:"
echo "- WordPress 管理后台: http://localhost:8080/wp-admin"
echo "- GraphQL 端点: http://localhost:8080/graphql"
echo "- GraphiQL 调试界面: http://localhost:8080/wp-admin/admin.php?page=graphiql-ide"
echo ""
