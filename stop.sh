#!/bin/bash

# 宠物用品电商网站停止脚本

set -e

echo "🛑 停止宠物用品电商网站..."

# 检查 Docker Compose 命令
if docker compose version &> /dev/null; then
    DOCKER_COMPOSE_CMD="docker compose"
elif command -v docker-compose &> /dev/null; then
    DOCKER_COMPOSE_CMD="docker-compose"
else
    echo "❌ Docker Compose 未找到"
    exit 1
fi

# 停止所有服务
echo "📦 停止 Docker 容器..."
$DOCKER_COMPOSE_CMD down

# 可选：清理未使用的镜像和容器
read -p "是否清理未使用的 Docker 资源？(y/N): " -n 1 -r
echo
if [[ $REPLY =~ ^[Yy]$ ]]; then
    echo "🧹 清理未使用的 Docker 资源..."
    docker system prune -f
    echo "✅ 清理完成"
fi

echo "✅ 所有服务已停止"
