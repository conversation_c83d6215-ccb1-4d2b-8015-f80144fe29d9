# ✅ 7个核心插件安装清单

## 🎯 安装清单 (按顺序安装)

### 1. WooCommerce ⭐⭐⭐⭐⭐
- [ ] 搜索: `WooCommerce`
- [ ] 安装并启用
- [ ] 验证: 左侧菜单出现 "WooCommerce"

### 2. WP GraphQL ⭐⭐⭐⭐⭐
- [ ] 搜索: `WPGraphQL`
- [ ] 安装并启用
- [ ] 验证: 访问 http://localhost:8080/graphql

### 3. WPGraphQL for WooCommerce ⭐⭐⭐⭐⭐
- [ ] 搜索: `WPGraphQL WooCommerce`
- [ ] 安装并启用
- [ ] 验证: GraphQL 支持 WooCommerce 查询

### 4. WP CORS ⭐⭐⭐⭐⭐
- [ ] 搜索: `WP CORS`
- [ ] 安装并启用
- [ ] 验证: 支持跨域请求

### 5. JWT Authentication ⭐⭐⭐⭐⭐
- [ ] 搜索: `JWT Authentication`
- [ ] 安装并启用
- [ ] 验证: 支持 JWT 令牌认证

### 6. WooCommerce Stripe Gateway ⭐⭐⭐⭐⭐
- [ ] 搜索: `WooCommerce Stripe`
- [ ] 安装并启用
- [ ] 验证: WooCommerce 支付设置中出现 Stripe

### 7. PayPal for WooCommerce ⭐⭐⭐⭐⭐
- [ ] 搜索: `PayPal WooCommerce`
- [ ] 安装并启用
- [ ] 验证: WooCommerce 支付设置中出现 PayPal

## 🔗 快速链接

- **插件安装页面**: http://localhost:8080/wp-admin/plugin-install.php
- **已安装插件**: http://localhost:8080/wp-admin/plugins.php
- **WooCommerce 设置**: http://localhost:8080/wp-admin/admin.php?page=wc-settings

## ✅ 安装完成后验证

所有插件安装完成后，检查:
- [ ] 7个插件都显示为"已启用"
- [ ] 左侧菜单出现 "WooCommerce"
- [ ] GraphQL 端点可访问
- [ ] 支付网关配置可用

**完成后你就拥有了完整的电商后端功能！** 🎉
