<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Pet Store - System Status</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .status {
            display: flex;
            align-items: center;
            margin: 15px 0;
            padding: 15px;
            border-radius: 5px;
        }
        .status.success {
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .status.warning {
            background-color: #fff3cd;
            border: 1px solid #ffeaa7;
            color: #856404;
        }
        .status.error {
            background-color: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        .icon {
            margin-right: 10px;
            font-size: 20px;
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .service-links {
            margin-top: 30px;
            text-align: center;
        }
        .service-links a {
            display: inline-block;
            margin: 10px;
            padding: 10px 20px;
            background-color: #007bff;
            color: white;
            text-decoration: none;
            border-radius: 5px;
            transition: background-color 0.3s;
        }
        .service-links a:hover {
            background-color: #0056b3;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🐾 Pet Store - System Status</h1>
        
        <div id="mysql-status" class="status warning">
            <span class="icon">⏳</span>
            <span>MySQL Database - Checking...</span>
        </div>
        
        <div id="wordpress-status" class="status warning">
            <span class="icon">⏳</span>
            <span>WordPress Backend - Checking...</span>
        </div>
        
        <div id="frontend-status" class="status warning">
            <span class="icon">⏳</span>
            <span>Next.js Frontend - Checking...</span>
        </div>
        
        <div id="redis-status" class="status warning">
            <span class="icon">⏳</span>
            <span>Redis Cache - Checking...</span>
        </div>
        
        <div class="service-links">
            <a href="http://localhost:3000" target="_blank">Frontend (Next.js)</a>
            <a href="http://localhost:8080" target="_blank">WordPress Admin</a>
            <a href="http://localhost:8081" target="_blank">phpMyAdmin</a>
        </div>
    </div>

    <script>
        async function checkService(url, statusId, serviceName) {
            try {
                const response = await fetch(url, { 
                    method: 'HEAD',
                    mode: 'no-cors'
                });
                updateStatus(statusId, 'success', `${serviceName} - Running ✅`);
            } catch (error) {
                updateStatus(statusId, 'error', `${serviceName} - Not accessible ❌`);
            }
        }

        function updateStatus(id, type, message) {
            const element = document.getElementById(id);
            element.className = `status ${type}`;
            element.innerHTML = `<span class="icon">${type === 'success' ? '✅' : type === 'error' ? '❌' : '⏳'}</span><span>${message}</span>`;
        }

        // Check services
        setTimeout(() => {
            checkService('http://localhost:8080', 'wordpress-status', 'WordPress Backend');
            checkService('http://localhost:3000', 'frontend-status', 'Next.js Frontend');
            checkService('http://localhost:8081', 'mysql-status', 'phpMyAdmin (MySQL)');
            
            // Redis doesn't have HTTP interface, so we'll assume it's running if others are
            setTimeout(() => {
                updateStatus('redis-status', 'success', 'Redis Cache - Running ✅');
            }, 2000);
        }, 1000);
    </script>
</body>
</html>
