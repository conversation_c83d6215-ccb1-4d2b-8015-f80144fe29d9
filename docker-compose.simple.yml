services:
  # MySQL 数据库
  mysql:
    image: mysql:8.0
    container_name: pet-store-mysql
    restart: unless-stopped
    environment:
      MYSQL_ROOT_PASSWORD: ${MYSQL_ROOT_PASSWORD}
      MYSQL_DATABASE: ${MYSQL_DATABASE}
      MYSQL_USER: ${MYSQL_USER}
      MYSQL_PASSWORD: ${MYSQL_PASSWORD}
    volumes:
      - mysql_data:/var/lib/mysql
    ports:
      - "3306:3306"
    networks:
      - pet-store-network
    command: --default-authentication-plugin=mysql_native_password

  # Redis 缓存
  redis:
    image: redis:7-alpine
    container_name: pet-store-redis
    restart: unless-stopped
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - pet-store-network
    command: redis-server --appendonly yes

  # WordPress 后端 (使用官方镜像)
  wordpress:
    image: wordpress:6.6-php8.3-apache
    container_name: pet-store-wordpress
    restart: unless-stopped
    environment:
      WORDPRESS_DB_HOST: mysql:3306
      WORDPRESS_DB_NAME: ${MYSQL_DATABASE}
      WORDPRESS_DB_USER: ${MYSQL_USER}
      WORDPRESS_DB_PASSWORD: ${MYSQL_PASSWORD}
      WORDPRESS_TABLE_PREFIX: wp_
      WORDPRESS_DEBUG: ${WORDPRESS_DEBUG:-false}
    volumes:
      - wordpress_data:/var/www/html
      - ./backend/uploads:/var/www/html/wp-content/uploads
    ports:
      - "8080:80"
    depends_on:
      - mysql
      - redis
    networks:
      - pet-store-network

  # Next.js 前端 (使用 Node.js 镜像直接运行)
  frontend:
    image: node:20-alpine
    container_name: pet-store-frontend
    restart: unless-stopped
    working_dir: /app
    environment:
      NODE_ENV: development
      NEXT_PUBLIC_WORDPRESS_URL: http://localhost:8080
      NEXT_PUBLIC_API_URL: http://localhost:8080/wp-json
      NEXTAUTH_SECRET: ${NEXTAUTH_SECRET}
      NEXTAUTH_URL: http://localhost:3000
    volumes:
      - ./frontend:/app
      - /app/node_modules
    ports:
      - "3000:3000"
    depends_on:
      - wordpress
    networks:
      - pet-store-network
    command: sh -c "npm install && npm run dev"

  # phpMyAdmin (开发环境)
  phpmyadmin:
    image: phpmyadmin/phpmyadmin:latest
    container_name: pet-store-phpmyadmin
    restart: unless-stopped
    environment:
      PMA_HOST: mysql
      PMA_PORT: 3306
      PMA_USER: ${MYSQL_USER}
      PMA_PASSWORD: ${MYSQL_PASSWORD}
    ports:
      - "8081:80"
    depends_on:
      - mysql
    networks:
      - pet-store-network

volumes:
  mysql_data:
  redis_data:
  wordpress_data:

networks:
  pet-store-network:
    driver: bridge
