<?php
/**
 * WordPress 自动配置脚本
 * 用于自动完成 WordPress 安装和基础配置
 */

// WordPress 安装配置
$config = [
    'site_title' => 'Pet Store - International Pet Supplies',
    'admin_user' => 'admin',
    'admin_password' => 'admin123',
    'admin_email' => '<EMAIL>',
    'site_url' => 'http://localhost:8080',
    'db_host' => 'mysql:3306',
    'db_name' => 'wordpress',
    'db_user' => 'wp_user',
    'db_password' => 'wppassword123',
];

// 检查是否已安装
if (file_exists('/var/www/html/wp-config.php')) {
    echo "WordPress 配置文件已存在\n";
    
    // 包含 WordPress
    require_once('/var/www/html/wp-config.php');
    require_once('/var/www/html/wp-load.php');
    
    // 检查是否已完成安装
    if (is_blog_installed()) {
        echo "WordPress 已安装完成\n";
        echo "管理员用户名: " . $config['admin_user'] . "\n";
        echo "管理员密码: " . $config['admin_password'] . "\n";
        echo "管理后台: " . $config['site_url'] . "/wp-admin\n";
    } else {
        echo "WordPress 需要完成安装\n";
        echo "请访问: " . $config['site_url'] . "\n";
    }
} else {
    echo "WordPress 配置文件不存在，需要手动安装\n";
    echo "请访问: " . $config['site_url'] . "\n";
}

// 输出配置信息
echo "\n=== WordPress 配置信息 ===\n";
echo "网站标题: " . $config['site_title'] . "\n";
echo "网站地址: " . $config['site_url'] . "\n";
echo "管理员用户名: " . $config['admin_user'] . "\n";
echo "管理员密码: " . $config['admin_password'] . "\n";
echo "管理员邮箱: " . $config['admin_email'] . "\n";
echo "数据库主机: " . $config['db_host'] . "\n";
echo "数据库名称: " . $config['db_name'] . "\n";
echo "数据库用户: " . $config['db_user'] . "\n";

// 推荐的插件列表
$recommended_plugins = [
    'woocommerce' => 'WooCommerce - 电商功能',
    'wp-graphql' => 'WPGraphQL - GraphQL API',
    'wp-graphql-woocommerce' => 'WooCommerce GraphQL',
    'advanced-custom-fields' => 'Advanced Custom Fields',
    'redis-cache' => 'Redis Object Cache',
    'woocommerce-gateway-stripe' => 'Stripe Payment Gateway',
    'woocommerce-paypal-payments' => 'PayPal Payments',
    'woocommerce-multilingual' => 'WooCommerce Multilingual',
    'wp-cors' => 'CORS Support',
];

echo "\n=== 推荐安装的插件 ===\n";
foreach ($recommended_plugins as $plugin => $description) {
    echo "- {$plugin}: {$description}\n";
}

echo "\n=== 下一步操作 ===\n";
echo "1. 访问 WordPress 管理后台: " . $config['site_url'] . "/wp-admin\n";
echo "2. 使用上述用户名和密码登录\n";
echo "3. 安装推荐的插件\n";
echo "4. 配置 WooCommerce 电商功能\n";
echo "5. 设置支付网关 (Stripe, PayPal)\n";
echo "6. 配置多语言和多货币支持\n";

?>
