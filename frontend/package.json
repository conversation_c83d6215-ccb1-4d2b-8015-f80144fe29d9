{"name": "pet-ecommerce-frontend", "version": "1.0.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "type-check": "tsc --noEmit"}, "dependencies": {"next": "^15.0.0", "react": "^18.3.0", "react-dom": "^18.3.0", "@tanstack/react-query": "^5.51.0", "@tanstack/react-query-devtools": "^5.51.0", "zustand": "^4.5.0", "next-auth": "^4.24.0", "axios": "^1.7.0", "graphql": "^16.9.0", "graphql-request": "^7.1.0", "@apollo/client": "^3.11.0", "clsx": "^2.1.0", "tailwind-merge": "^2.4.0", "class-variance-authority": "^0.7.0", "lucide-react": "^0.427.0", "@radix-ui/react-slot": "^1.1.0", "@radix-ui/react-dialog": "^1.1.0", "@radix-ui/react-dropdown-menu": "^2.1.0", "@radix-ui/react-toast": "^1.2.0", "@radix-ui/react-tabs": "^1.1.0", "@radix-ui/react-select": "^2.1.0", "@radix-ui/react-checkbox": "^1.1.0", "@radix-ui/react-radio-group": "^1.2.0", "@radix-ui/react-slider": "^1.2.0", "react-hook-form": "^7.52.0", "@hookform/resolvers": "^3.9.0", "zod": "^3.23.0", "date-fns": "^3.6.0", "react-intersection-observer": "^9.13.0", "framer-motion": "^11.3.0", "sharp": "^0.33.0", "next-themes": "^0.3.0", "@stripe/stripe-js": "^4.1.0", "@stripe/react-stripe-js": "^2.7.0", "@paypal/react-paypal-js": "^8.5.0", "next-intl": "^3.17.0", "currency.js": "^2.0.4", "react-currency-input-field": "^3.8.0", "country-list": "^2.3.0", "react-phone-number-input": "^3.4.0", "libphonenumber-js": "^1.11.0", "react-select-country-list": "^2.2.3"}, "devDependencies": {"typescript": "^5.5.0", "@types/node": "^22.0.0", "@types/react": "^18.3.0", "@types/react-dom": "^18.3.0", "tailwindcss": "^3.4.0", "postcss": "^8.4.0", "autoprefixer": "^10.4.0", "eslint": "^8.57.0", "eslint-config-next": "^15.0.0", "@typescript-eslint/eslint-plugin": "^8.0.0", "@typescript-eslint/parser": "^8.0.0", "prettier": "^3.3.0", "prettier-plugin-tailwindcss": "^0.6.0"}, "engines": {"node": ">=18.17.0", "npm": ">=9.0.0"}}