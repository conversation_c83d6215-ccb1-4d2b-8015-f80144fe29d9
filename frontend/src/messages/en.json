{"common": {"loading": "Loading...", "error": "An error occurred", "success": "Success!", "cancel": "Cancel", "confirm": "Confirm", "save": "Save", "delete": "Delete", "edit": "Edit", "add": "Add", "search": "Search", "filter": "Filter", "sort": "Sort", "next": "Next", "previous": "Previous", "close": "Close"}, "navigation": {"home": "Home", "products": "Products", "categories": "Categories", "cart": "<PERSON><PERSON>", "account": "Account", "login": "<PERSON><PERSON>", "register": "Register", "logout": "Logout", "contact": "Contact", "about": "About"}, "product": {"title": "Products", "price": "Price", "addToCart": "Add to Cart", "outOfStock": "Out of Stock", "inStock": "In Stock", "description": "Description", "specifications": "Specifications", "reviews": "Reviews", "relatedProducts": "Related Products", "quantity": "Quantity"}, "cart": {"title": "Shopping Cart", "empty": "Your cart is empty", "total": "Total", "subtotal": "Subtotal", "shipping": "Shipping", "tax": "Tax", "checkout": "Checkout", "continueShopping": "Continue Shopping", "removeItem": "Remove Item", "updateQuantity": "Update Quantity"}, "checkout": {"title": "Checkout", "billingAddress": "Billing Address", "shippingAddress": "Shipping Address", "paymentMethod": "Payment Method", "orderSummary": "Order Summary", "placeOrder": "Place Order", "processing": "Processing...", "firstName": "First Name", "lastName": "Last Name", "email": "Email", "phone": "Phone", "address": "Address", "city": "City", "state": "State", "zipCode": "ZIP Code", "country": "Country"}, "payment": {"creditCard": "Credit Card", "paypal": "PayPal", "applePay": "Apple Pay", "googlePay": "Google Pay", "cardNumber": "Card Number", "expiryDate": "Expiry Date", "cvv": "CVV", "cardholderName": "Cardholder Name", "paymentFailed": "Payment failed. Please try again.", "paymentSuccess": "Payment successful!", "processing": "Processing payment..."}, "account": {"title": "My Account", "profile": "Profile", "orders": "Orders", "addresses": "Addresses", "wishlist": "Wishlist", "settings": "Settings", "orderHistory": "Order History", "orderNumber": "Order #", "orderDate": "Order Date", "orderStatus": "Status", "orderTotal": "Total"}, "currency": {"usd": "US Dollar", "eur": "Euro", "gbp": "British Pound", "cad": "Canadian Dollar", "aud": "Australian Dollar", "jpy": "Japanese Yen", "krw": "Korean Won", "changeCurrency": "Change Currency", "currentCurrency": "Current Currency"}, "language": {"english": "English", "french": "Français", "german": "De<PERSON>ch", "spanish": "Español", "italian": "Italiano", "japanese": "日本語", "korean": "한국어", "changeLanguage": "Change Language", "currentLanguage": "Current Language"}, "pets": {"dogs": "Dogs", "cats": "Cats", "birds": "Birds", "fish": "Fish", "reptiles": "Reptiles", "smallPets": "Small Pets", "food": "Food", "toys": "Toys", "accessories": "Accessories", "healthcare": "Healthcare", "grooming": "Grooming"}, "footer": {"customerService": "Customer Service", "shippingInfo": "Shipping Information", "returnPolicy": "Return Policy", "privacyPolicy": "Privacy Policy", "termsOfService": "Terms of Service", "followUs": "Follow Us", "newsletter": "Newsletter", "subscribeNewsletter": "Subscribe to our newsletter for updates and special offers"}}