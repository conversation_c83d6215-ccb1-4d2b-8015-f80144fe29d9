import { notFound } from 'next/navigation';
import { getRequestConfig } from 'next-intl/server';

// 支持的语言列表
export const locales = ['en', 'fr', 'de', 'es', 'it', 'ja', 'ko'] as const;
export type Locale = (typeof locales)[number];

// 默认语言
export const defaultLocale: Locale = 'en';

// 语言配置
export const localeConfig = {
  en: {
    name: 'English',
    flag: '🇺🇸',
    currency: 'USD',
    currencySymbol: '$',
    dateFormat: 'MM/dd/yyyy',
    timeZone: 'America/New_York',
  },
  fr: {
    name: 'Français',
    flag: '🇫🇷',
    currency: 'EUR',
    currencySymbol: '€',
    dateFormat: 'dd/MM/yyyy',
    timeZone: 'Europe/Paris',
  },
  de: {
    name: 'Deutsch',
    flag: '🇩🇪',
    currency: 'EUR',
    currencySymbol: '€',
    dateFormat: 'dd.MM.yyyy',
    timeZone: 'Europe/Berlin',
  },
  es: {
    name: 'Español',
    flag: '🇪🇸',
    currency: 'EUR',
    currencySymbol: '€',
    dateFormat: 'dd/MM/yyyy',
    timeZone: 'Europe/Madrid',
  },
  it: {
    name: 'Italiano',
    flag: '🇮🇹',
    currency: 'EUR',
    currencySymbol: '€',
    dateFormat: 'dd/MM/yyyy',
    timeZone: 'Europe/Rome',
  },
  ja: {
    name: '日本語',
    flag: '🇯🇵',
    currency: 'JPY',
    currencySymbol: '¥',
    dateFormat: 'yyyy/MM/dd',
    timeZone: 'Asia/Tokyo',
  },
  ko: {
    name: '한국어',
    flag: '🇰🇷',
    currency: 'KRW',
    currencySymbol: '₩',
    dateFormat: 'yyyy.MM.dd',
    timeZone: 'Asia/Seoul',
  },
} as const;

// 货币配置
export const currencyConfig = {
  USD: { symbol: '$', decimals: 2, name: 'US Dollar' },
  EUR: { symbol: '€', decimals: 2, name: 'Euro' },
  GBP: { symbol: '£', decimals: 2, name: 'British Pound' },
  CAD: { symbol: 'C$', decimals: 2, name: 'Canadian Dollar' },
  AUD: { symbol: 'A$', decimals: 2, name: 'Australian Dollar' },
  JPY: { symbol: '¥', decimals: 0, name: 'Japanese Yen' },
  KRW: { symbol: '₩', decimals: 0, name: 'Korean Won' },
} as const;

export type Currency = keyof typeof currencyConfig;

export default getRequestConfig(async ({ locale }) => {
  // 验证传入的语言是否支持
  if (!locales.includes(locale as Locale)) notFound();

  return {
    messages: (await import(`../messages/${locale}.json`)).default,
  };
});
