import currency from 'currency.js';
import { currencyConfig, type Currency } from './i18n';

// 汇率缓存
let exchangeRates: Record<string, number> = {};
let lastFetchTime = 0;
const CACHE_DURATION = 60 * 60 * 1000; // 1小时

// 获取汇率
export const getExchangeRates = async (baseCurrency: Currency = 'USD'): Promise<Record<string, number>> => {
  const now = Date.now();
  
  // 如果缓存有效，直接返回
  if (exchangeRates[baseCurrency] && (now - lastFetchTime) < CACHE_DURATION) {
    return exchangeRates;
  }

  try {
    const response = await fetch(`/api/exchange-rates?base=${baseCurrency}`);
    if (!response.ok) {
      throw new Error('Failed to fetch exchange rates');
    }
    
    const data = await response.json();
    exchangeRates = data.rates;
    lastFetchTime = now;
    
    return exchangeRates;
  } catch (error) {
    console.error('Error fetching exchange rates:', error);
    
    // 返回默认汇率（如果缓存为空）
    if (Object.keys(exchangeRates).length === 0) {
      return getDefaultExchangeRates(baseCurrency);
    }
    
    return exchangeRates;
  }
};

// 默认汇率（作为后备）
const getDefaultExchangeRates = (baseCurrency: Currency): Record<string, number> => {
  const defaultRates: Record<Currency, Record<string, number>> = {
    USD: { USD: 1, EUR: 0.85, GBP: 0.73, CAD: 1.25, AUD: 1.35, JPY: 110, KRW: 1200 },
    EUR: { USD: 1.18, EUR: 1, GBP: 0.86, CAD: 1.47, AUD: 1.59, JPY: 129, KRW: 1412 },
    GBP: { USD: 1.37, EUR: 1.16, GBP: 1, CAD: 1.71, AUD: 1.85, JPY: 151, KRW: 1644 },
    CAD: { USD: 0.80, EUR: 0.68, GBP: 0.58, CAD: 1, AUD: 1.08, JPY: 88, KRW: 960 },
    AUD: { USD: 0.74, EUR: 0.63, GBP: 0.54, CAD: 0.93, AUD: 1, JPY: 81, KRW: 888 },
    JPY: { USD: 0.0091, EUR: 0.0077, GBP: 0.0066, CAD: 0.011, AUD: 0.012, JPY: 1, KRW: 10.9 },
    KRW: { USD: 0.00083, EUR: 0.00071, GBP: 0.00061, CAD: 0.001, AUD: 0.0011, JPY: 0.092, KRW: 1 },
  };
  
  return defaultRates[baseCurrency] || defaultRates.USD;
};

// 货币转换
export const convertCurrency = async (
  amount: number,
  fromCurrency: Currency,
  toCurrency: Currency
): Promise<number> => {
  if (fromCurrency === toCurrency) {
    return amount;
  }

  const rates = await getExchangeRates(fromCurrency);
  const rate = rates[toCurrency];
  
  if (!rate) {
    throw new Error(`Exchange rate not found for ${fromCurrency} to ${toCurrency}`);
  }

  return amount * rate;
};

// 格式化货币显示
export const formatCurrency = (
  amount: number,
  currencyCode: Currency,
  locale?: string
): string => {
  const config = currencyConfig[currencyCode];
  
  if (!config) {
    throw new Error(`Currency config not found for ${currencyCode}`);
  }

  // 使用 currency.js 进行精确计算和格式化
  const currencyAmount = currency(amount, {
    symbol: config.symbol,
    precision: config.decimals,
  });

  // 如果提供了 locale，使用 Intl.NumberFormat
  if (locale) {
    return new Intl.NumberFormat(locale, {
      style: 'currency',
      currency: currencyCode,
      minimumFractionDigits: config.decimals,
      maximumFractionDigits: config.decimals,
    }).format(amount);
  }

  return currencyAmount.format();
};

// 解析货币字符串为数字
export const parseCurrency = (
  value: string,
  currencyCode: Currency
): number => {
  const config = currencyConfig[currencyCode];
  
  if (!config) {
    throw new Error(`Currency config not found for ${currencyCode}`);
  }

  // 移除货币符号和格式化字符
  const cleanValue = value
    .replace(new RegExp(`\\${config.symbol}`, 'g'), '')
    .replace(/[,\s]/g, '')
    .trim();

  const parsed = parseFloat(cleanValue);
  
  if (isNaN(parsed)) {
    throw new Error(`Invalid currency value: ${value}`);
  }

  return parsed;
};

// 获取货币列表
export const getCurrencyList = (): Array<{
  code: Currency;
  name: string;
  symbol: string;
  decimals: number;
}> => {
  return Object.entries(currencyConfig).map(([code, config]) => ({
    code: code as Currency,
    name: config.name,
    symbol: config.symbol,
    decimals: config.decimals,
  }));
};

// 验证货币代码
export const isValidCurrency = (code: string): code is Currency => {
  return code in currencyConfig;
};

// 获取用户首选货币（基于地理位置或浏览器设置）
export const getUserPreferredCurrency = (): Currency => {
  // 尝试从 localStorage 获取用户设置
  if (typeof window !== 'undefined') {
    const saved = localStorage.getItem('preferred-currency');
    if (saved && isValidCurrency(saved)) {
      return saved;
    }

    // 基于浏览器语言推断货币
    const locale = navigator.language || 'en-US';
    const currencyMap: Record<string, Currency> = {
      'en-US': 'USD',
      'en-GB': 'GBP',
      'en-CA': 'CAD',
      'en-AU': 'AUD',
      'fr': 'EUR',
      'de': 'EUR',
      'es': 'EUR',
      'it': 'EUR',
      'ja': 'JPY',
      'ko': 'KRW',
    };

    const inferredCurrency = currencyMap[locale] || currencyMap[locale.split('-')[0]];
    return inferredCurrency || 'USD';
  }

  return 'USD';
};

// 保存用户货币偏好
export const saveUserCurrencyPreference = (currency: Currency): void => {
  if (typeof window !== 'undefined') {
    localStorage.setItem('preferred-currency', currency);
  }
};
