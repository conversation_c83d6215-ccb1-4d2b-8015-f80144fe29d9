import { loadStripe, Stripe } from '@stripe/stripe-js';

// Stripe 配置
let stripePromise: Promise<Stripe | null>;

export const getStripe = () => {
  if (!stripePromise) {
    stripePromise = loadStripe(process.env.NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY!);
  }
  return stripePromise;
};

// 支付方式配置
export const paymentMethods = {
  stripe: {
    name: 'Credit Card',
    description: 'Pay with Visa, Mastercard, American Express',
    icon: '💳',
    enabled: true,
    currencies: ['USD', 'EUR', 'GBP', 'CAD', 'AUD', 'JPY'],
  },
  paypal: {
    name: 'PayPal',
    description: 'Pay with your PayPal account',
    icon: '🅿️',
    enabled: true,
    currencies: ['USD', 'EUR', 'GBP', 'CAD', 'AUD', 'JPY'],
  },
  applePay: {
    name: 'Apple Pay',
    description: 'Pay with Touch ID or Face ID',
    icon: '🍎',
    enabled: true,
    currencies: ['USD', 'EUR', 'GBP', 'CAD', 'AUD', 'JPY'],
    requiresHttps: true,
  },
  googlePay: {
    name: 'Google Pay',
    description: 'Pay with Google Pay',
    icon: '🔵',
    enabled: true,
    currencies: ['USD', 'EUR', 'GBP', 'CAD', 'AUD', 'JPY'],
  },
} as const;

// PayPal 配置
export const paypalConfig = {
  clientId: process.env.NEXT_PUBLIC_PAYPAL_CLIENT_ID!,
  currency: 'USD',
  intent: 'capture',
  components: 'buttons,marks,funding-eligibility',
  enableFunding: 'venmo,paylater',
  disableFunding: '',
  dataClientToken: '',
};

// Apple Pay 配置
export const applePayConfig = {
  countryCode: 'US',
  currencyCode: 'USD',
  merchantCapabilities: ['supports3DS', 'supportsCredit', 'supportsDebit'],
  supportedNetworks: ['visa', 'masterCard', 'amex', 'discover'],
};

// Google Pay 配置
export const googlePayConfig = {
  environment: process.env.NODE_ENV === 'production' ? 'PRODUCTION' : 'TEST',
  merchantInfo: {
    merchantId: process.env.NEXT_PUBLIC_GOOGLE_PAY_MERCHANT_ID,
    merchantName: process.env.NEXT_PUBLIC_GOOGLE_PAY_MERCHANT_NAME || 'Pet Store',
  },
  allowedPaymentMethods: [
    {
      type: 'CARD',
      parameters: {
        allowedAuthMethods: ['PAN_ONLY', 'CRYPTOGRAM_3DS'],
        allowedCardNetworks: ['MASTERCARD', 'VISA', 'AMEX'],
      },
      tokenizationSpecification: {
        type: 'PAYMENT_GATEWAY',
        parameters: {
          gateway: 'stripe',
          'stripe:version': '2020-08-27',
          'stripe:publishableKey': process.env.NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY,
        },
      },
    },
  ],
};

// 支付处理函数
export const processPayment = async (
  method: keyof typeof paymentMethods,
  amount: number,
  currency: string,
  metadata?: Record<string, any>
) => {
  switch (method) {
    case 'stripe':
      return processStripePayment(amount, currency, metadata);
    case 'paypal':
      return processPayPalPayment(amount, currency, metadata);
    case 'applePay':
      return processApplePayPayment(amount, currency, metadata);
    case 'googlePay':
      return processGooglePayPayment(amount, currency, metadata);
    default:
      throw new Error(`Unsupported payment method: ${method}`);
  }
};

// Stripe 支付处理
const processStripePayment = async (
  amount: number,
  currency: string,
  metadata?: Record<string, any>
) => {
  const response = await fetch('/api/payments/stripe/create-payment-intent', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({
      amount: Math.round(amount * 100), // 转换为分
      currency: currency.toLowerCase(),
      metadata,
    }),
  });

  if (!response.ok) {
    throw new Error('Failed to create payment intent');
  }

  return response.json();
};

// PayPal 支付处理
const processPayPalPayment = async (
  amount: number,
  currency: string,
  metadata?: Record<string, any>
) => {
  const response = await fetch('/api/payments/paypal/create-order', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({
      amount,
      currency,
      metadata,
    }),
  });

  if (!response.ok) {
    throw new Error('Failed to create PayPal order');
  }

  return response.json();
};

// Apple Pay 支付处理
const processApplePayPayment = async (
  amount: number,
  currency: string,
  metadata?: Record<string, any>
) => {
  // Apple Pay 通过 Stripe 处理
  return processStripePayment(amount, currency, {
    ...metadata,
    payment_method_types: ['apple_pay'],
  });
};

// Google Pay 支付处理
const processGooglePayPayment = async (
  amount: number,
  currency: string,
  metadata?: Record<string, any>
) => {
  // Google Pay 通过 Stripe 处理
  return processStripePayment(amount, currency, {
    ...metadata,
    payment_method_types: ['google_pay'],
  });
};

// 验证支付方式是否支持指定货币
export const isPaymentMethodSupported = (
  method: keyof typeof paymentMethods,
  currency: string
): boolean => {
  return paymentMethods[method].currencies.includes(currency as any);
};

// 获取可用的支付方式
export const getAvailablePaymentMethods = (currency: string) => {
  return Object.entries(paymentMethods).filter(([_, config]) =>
    config.enabled && config.currencies.includes(currency as any)
  );
};
