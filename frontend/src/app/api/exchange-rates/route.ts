import { NextRequest, NextResponse } from 'next/server';

// 汇率缓存
let cachedRates: Record<string, any> = {};
let lastFetchTime = 0;
const CACHE_DURATION = 60 * 60 * 1000; // 1小时

// 支持的汇率提供商
const EXCHANGE_RATE_PROVIDERS = {
  fixer: 'https://api.fixer.io/v1/latest',
  exchangerate: 'https://api.exchangerate-api.com/v4/latest',
  currencylayer: 'https://api.currencylayer.com/live',
} as const;

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const baseCurrency = searchParams.get('base') || 'USD';
    const provider = searchParams.get('provider') || 'exchangerate';

    // 检查缓存
    const now = Date.now();
    const cacheKey = `${provider}_${baseCurrency}`;
    
    if (cachedRates[cacheKey] && (now - lastFetchTime) < CACHE_DURATION) {
      return NextResponse.json({
        success: true,
        base: baseCurrency,
        rates: cachedRates[cacheKey],
        cached: true,
        timestamp: lastFetchTime,
      });
    }

    // 获取新的汇率数据
    const rates = await fetchExchangeRates(baseCurrency, provider as keyof typeof EXCHANGE_RATE_PROVIDERS);
    
    // 更新缓存
    cachedRates[cacheKey] = rates;
    lastFetchTime = now;

    return NextResponse.json({
      success: true,
      base: baseCurrency,
      rates,
      cached: false,
      timestamp: now,
    });

  } catch (error) {
    console.error('Error fetching exchange rates:', error);
    
    // 返回默认汇率作为后备
    const defaultRates = getDefaultRates(
      (new URL(request.url)).searchParams.get('base') || 'USD'
    );

    return NextResponse.json({
      success: false,
      error: 'Failed to fetch exchange rates',
      base: (new URL(request.url)).searchParams.get('base') || 'USD',
      rates: defaultRates,
      fallback: true,
    }, { status: 200 }); // 返回 200 以便前端可以使用默认汇率
  }
}

async function fetchExchangeRates(
  baseCurrency: string,
  provider: keyof typeof EXCHANGE_RATE_PROVIDERS
): Promise<Record<string, number>> {
  const apiKey = process.env.EXCHANGE_RATE_API_KEY;
  
  switch (provider) {
    case 'fixer':
      return fetchFromFixer(baseCurrency, apiKey);
    case 'exchangerate':
      return fetchFromExchangeRate(baseCurrency);
    case 'currencylayer':
      return fetchFromCurrencyLayer(baseCurrency, apiKey);
    default:
      throw new Error(`Unsupported provider: ${provider}`);
  }
}

async function fetchFromFixer(baseCurrency: string, apiKey?: string): Promise<Record<string, number>> {
  if (!apiKey) {
    throw new Error('Fixer.io API key is required');
  }

  const url = `${EXCHANGE_RATE_PROVIDERS.fixer}?access_key=${apiKey}&base=${baseCurrency}`;
  const response = await fetch(url);
  
  if (!response.ok) {
    throw new Error(`Fixer.io API error: ${response.status}`);
  }

  const data = await response.json();
  
  if (!data.success) {
    throw new Error(`Fixer.io API error: ${data.error?.info || 'Unknown error'}`);
  }

  return data.rates;
}

async function fetchFromExchangeRate(baseCurrency: string): Promise<Record<string, number>> {
  const url = `${EXCHANGE_RATE_PROVIDERS.exchangerate}/${baseCurrency}`;
  const response = await fetch(url);
  
  if (!response.ok) {
    throw new Error(`ExchangeRate-API error: ${response.status}`);
  }

  const data = await response.json();
  return data.rates;
}

async function fetchFromCurrencyLayer(baseCurrency: string, apiKey?: string): Promise<Record<string, number>> {
  if (!apiKey) {
    throw new Error('CurrencyLayer API key is required');
  }

  const url = `${EXCHANGE_RATE_PROVIDERS.currencylayer}?access_key=${apiKey}&source=${baseCurrency}`;
  const response = await fetch(url);
  
  if (!response.ok) {
    throw new Error(`CurrencyLayer API error: ${response.status}`);
  }

  const data = await response.json();
  
  if (!data.success) {
    throw new Error(`CurrencyLayer API error: ${data.error?.info || 'Unknown error'}`);
  }

  // CurrencyLayer 返回的格式是 USDEUR, USDGBP 等，需要转换
  const rates: Record<string, number> = {};
  Object.entries(data.quotes).forEach(([key, value]) => {
    const currency = key.substring(3); // 移除前缀（如 USD）
    rates[currency] = value as number;
  });

  return rates;
}

function getDefaultRates(baseCurrency: string): Record<string, number> {
  const defaultRates: Record<string, Record<string, number>> = {
    USD: { USD: 1, EUR: 0.85, GBP: 0.73, CAD: 1.25, AUD: 1.35, JPY: 110, KRW: 1200 },
    EUR: { USD: 1.18, EUR: 1, GBP: 0.86, CAD: 1.47, AUD: 1.59, JPY: 129, KRW: 1412 },
    GBP: { USD: 1.37, EUR: 1.16, GBP: 1, CAD: 1.71, AUD: 1.85, JPY: 151, KRW: 1644 },
    CAD: { USD: 0.80, EUR: 0.68, GBP: 0.58, CAD: 1, AUD: 1.08, JPY: 88, KRW: 960 },
    AUD: { USD: 0.74, EUR: 0.63, GBP: 0.54, CAD: 0.93, AUD: 1, JPY: 81, KRW: 888 },
    JPY: { USD: 0.0091, EUR: 0.0077, GBP: 0.0066, CAD: 0.011, AUD: 0.012, JPY: 1, KRW: 10.9 },
    KRW: { USD: 0.00083, EUR: 0.00071, GBP: 0.00061, CAD: 0.001, AUD: 0.0011, JPY: 0.092, KRW: 1 },
  };

  return defaultRates[baseCurrency] || defaultRates.USD;
}
