export default function Home() {
  return (
    <main className="flex min-h-screen flex-col items-center justify-between p-24">
      <div className="z-10 max-w-5xl w-full items-center justify-between font-mono text-sm lg:flex">
        <h1 className="text-4xl font-bold text-center">
          欢迎来到宠物用品商店
        </h1>
      </div>

      <div className="relative flex place-items-center">
        <div className="text-center">
          <h2 className="text-2xl font-semibold mb-4">
            专业的宠物用品电商平台
          </h2>
          <p className="text-lg text-gray-600">
            基于 Next.js + WordPress 构建的现代化电商解决方案
          </p>
        </div>
      </div>

      <div className="mb-32 grid text-center lg:max-w-5xl lg:w-full lg:mb-0 lg:grid-cols-4 lg:text-left">
        <div className="group rounded-lg border border-transparent px-5 py-4 transition-colors hover:border-gray-300 hover:bg-gray-100">
          <h3 className="mb-3 text-2xl font-semibold">
            产品展示
          </h3>
          <p className="m-0 max-w-[30ch] text-sm opacity-50">
            丰富的宠物用品展示和分类浏览
          </p>
        </div>

        <div className="group rounded-lg border border-transparent px-5 py-4 transition-colors hover:border-gray-300 hover:bg-gray-100">
          <h3 className="mb-3 text-2xl font-semibold">
            购物车
          </h3>
          <p className="m-0 max-w-[30ch] text-sm opacity-50">
            便捷的购物车和结算功能
          </p>
        </div>

        <div className="group rounded-lg border border-transparent px-5 py-4 transition-colors hover:border-gray-300 hover:bg-gray-100">
          <h3 className="mb-3 text-2xl font-semibold">
            用户中心
          </h3>
          <p className="m-0 max-w-[30ch] text-sm opacity-50">
            完整的用户注册、登录和个人中心
          </p>
        </div>

        <div className="group rounded-lg border border-transparent px-5 py-4 transition-colors hover:border-gray-300 hover:bg-gray-100">
          <h3 className="mb-3 text-2xl font-semibold">
            订单管理
          </h3>
          <p className="m-0 max-w-[30ch] text-sm opacity-50">
            订单跟踪和管理功能
          </p>
        </div>
      </div>
    </main>
  )
}
