{"version": 3, "caseSensitive": false, "basePath": "", "rewrites": {"beforeFiles": [], "afterFiles": [{"source": "/api/wp/:path*", "destination": "http://localhost:8080/wp-json/:path*", "regex": "^\\/api\\/wp(?:\\/((?:[^\\/]+?)(?:\\/(?:[^\\/]+?))*))?(?:\\/)?$", "check": true}, {"source": "/wp-content/:path*", "destination": "http://localhost:8080/wp-content/:path*", "regex": "^\\/wp-content(?:\\/((?:[^\\/]+?)(?:\\/(?:[^\\/]+?))*))?(?:\\/)?$", "check": true}], "fallback": []}, "redirects": [{"source": "/:path+/", "destination": "/:path+", "permanent": true, "internal": true, "regex": "^(?:\\/((?:[^\\/]+?)(?:\\/(?:[^\\/]+?))*))\\/$"}], "headers": [{"source": "/(.*)", "headers": [{"key": "X-Frame-Options", "value": "DENY"}, {"key": "X-Content-Type-Options", "value": "nosniff"}, {"key": "Referrer-Policy", "value": "strict-origin-when-cross-origin"}], "regex": "^(?:\\/(.*))(?:\\/)?$"}]}