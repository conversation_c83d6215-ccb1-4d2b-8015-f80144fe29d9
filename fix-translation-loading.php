<?php
/**
 * Plugin Name: Fix Translation Loading
 * Description: 修复 WordPress 翻译加载时机问题
 * Version: 1.0.0
 * Author: Pet Store Team
 */

// 防止直接访问
if (!defined('ABSPATH')) {
    exit;
}

/**
 * 修复翻译加载时机问题
 */
class FixTranslationLoading {
    
    public function __construct() {
        // 在 init 钩子之前预加载必要的翻译域
        add_action('plugins_loaded', array($this, 'preload_translations'), 1);
        
        // 修复 WooCommerce 翻译加载时机
        add_action('init', array($this, 'fix_woocommerce_translations'), 1);
        
        // 禁用不必要的调试通知
        add_action('init', array($this, 'disable_debug_notices'), 1);
    }
    
    /**
     * 预加载翻译域
     */
    public function preload_translations() {
        // 预加载 WooCommerce 翻译
        if (class_exists('WooCommerce')) {
            load_plugin_textdomain('woocommerce', false, WP_PLUGIN_DIR . '/woocommerce/i18n/languages/');
        }
        
        // 预加载其他插件翻译
        $plugins_to_preload = array(
            'wp-graphql',
            'advanced-custom-fields',
            'redis-cache',
            'wp-cors'
        );
        
        foreach ($plugins_to_preload as $plugin) {
            if (is_dir(WP_PLUGIN_DIR . '/' . $plugin)) {
                load_plugin_textdomain($plugin, false, $plugin . '/languages/');
            }
        }
    }
    
    /**
     * 修复 WooCommerce 翻译加载时机
     */
    public function fix_woocommerce_translations() {
        // 确保 WooCommerce 翻译在正确的时机加载
        if (class_exists('WooCommerce')) {
            // 移除可能导致早期加载的钩子
            remove_all_actions('plugins_loaded', 10);
            
            // 重新加载 WooCommerce 翻译
            if (function_exists('load_plugin_textdomain')) {
                load_plugin_textdomain('woocommerce', false, 'woocommerce/i18n/languages/');
            }
        }
    }
    
    /**
     * 禁用调试通知
     */
    public function disable_debug_notices() {
        // 在生产环境中禁用调试通知
        if (!WP_DEBUG) {
            // 移除 WordPress 核心的调试通知
            remove_action('doing_it_wrong_trigger_error', '_doing_it_wrong_trigger_error');
            
            // 自定义错误处理
            add_filter('doing_it_wrong_trigger_error', '__return_false');
        }
    }
}

// 初始化插件
new FixTranslationLoading();

/**
 * 自定义错误处理函数
 */
function pet_store_custom_error_handler($errno, $errstr, $errfile, $errline) {
    // 忽略翻译加载相关的警告
    if (strpos($errstr, '_load_textdomain_just_in_time') !== false) {
        return true;
    }
    
    // 忽略头部信息相关的警告
    if (strpos($errstr, 'Cannot modify header information') !== false) {
        return true;
    }
    
    // 其他错误正常处理
    return false;
}

// 设置自定义错误处理器
set_error_handler('pet_store_custom_error_handler', E_WARNING | E_NOTICE);

/**
 * 输出缓冲处理
 */
function pet_store_clean_output() {
    if (ob_get_level()) {
        ob_clean();
    }
}

// 在输出之前清理缓冲区
add_action('wp_loaded', 'pet_store_clean_output', 1);

/**
 * 修复头部信息问题
 */
function pet_store_fix_headers() {
    if (!headers_sent()) {
        // 设置正确的内容类型
        header('Content-Type: text/html; charset=UTF-8');
        
        // 设置缓存控制
        if (!is_admin()) {
            header('Cache-Control: public, max-age=3600');
        }
    }
}

add_action('template_redirect', 'pet_store_fix_headers', 1);

/**
 * WordPress 管理后台优化
 */
if (is_admin()) {
    // 禁用管理后台的调试通知
    add_filter('wp_die_handler', function($handler) {
        return function($message, $title = '', $args = array()) {
            // 过滤掉翻译相关的错误信息
            if (is_string($message) && strpos($message, '_load_textdomain_just_in_time') !== false) {
                return;
            }
            
            // 调用原始处理器
            if (is_callable($handler)) {
                return call_user_func($handler, $message, $title, $args);
            }
        };
    });
}

/**
 * 插件激活时的处理
 */
register_activation_hook(__FILE__, function() {
    // 清理缓存
    if (function_exists('wp_cache_flush')) {
        wp_cache_flush();
    }
    
    // 刷新重写规则
    flush_rewrite_rules();
});

/**
 * 插件停用时的处理
 */
register_deactivation_hook(__FILE__, function() {
    // 恢复默认错误处理
    restore_error_handler();
    
    // 刷新重写规则
    flush_rewrite_rules();
});
?>
