# 环境变量配置文件示例
# 复制此文件为 .env 并填入实际值

# ===========================================
# 数据库配置
# ===========================================
MYSQL_ROOT_PASSWORD=your_strong_root_password_here
MYSQL_DATABASE=wordpress
MYSQL_USER=wp_user
MYSQL_PASSWORD=your_strong_wp_password_here

# ===========================================
# WordPress 配置
# ===========================================
WORDPRESS_DEBUG=false
WORDPRESS_DB_HOST=mysql:3306
WORDPRESS_DB_NAME=wordpress
WORDPRESS_DB_USER=wp_user
WORDPRESS_DB_PASSWORD=your_strong_wp_password_here
WORDPRESS_TABLE_PREFIX=wp_

# WordPress 安全密钥 (请生成唯一值)
# 可以从 https://api.wordpress.org/secret-key/1.1/salt/ 获取
WP_AUTH_KEY=your_unique_auth_key_here
WP_SECURE_AUTH_KEY=your_unique_secure_auth_key_here
WP_LOGGED_IN_KEY=your_unique_logged_in_key_here
WP_NONCE_KEY=your_unique_nonce_key_here
WP_AUTH_SALT=your_unique_auth_salt_here
WP_SECURE_AUTH_SALT=your_unique_secure_auth_salt_here
WP_LOGGED_IN_SALT=your_unique_logged_in_salt_here
WP_NONCE_SALT=your_unique_nonce_salt_here

# ===========================================
# Next.js 前端配置
# ===========================================
NODE_ENV=development
NEXT_PUBLIC_API_URL=http://localhost:8080/wp-json
NEXT_PUBLIC_WORDPRESS_URL=http://localhost:8080
NEXT_PUBLIC_WORDPRESS_DOMAIN=localhost:8080
NEXT_PUBLIC_GRAPHQL_URL=http://localhost:8080/graphql
NEXT_PUBLIC_SITE_URL=http://localhost:3000

# NextAuth 配置
NEXTAUTH_SECRET=your_nextauth_secret_key_here
NEXTAUTH_URL=http://localhost:3000
NEXTAUTH_JWT_SECRET=your_jwt_secret_here

# ===========================================
# Redis 配置
# ===========================================
WP_REDIS_HOST=redis
WP_REDIS_PORT=6379
WP_REDIS_PASSWORD=
WP_REDIS_DATABASE=0

# ===========================================
# 邮件配置 (可选)
# ===========================================
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASSWORD=your_app_password
SMTP_FROM=<EMAIL>
SMTP_FROM_NAME=宠物用品商店

# ===========================================
# 支付配置 (生产环境)
# ===========================================
# Stripe 支付
STRIPE_PUBLISHABLE_KEY=pk_test_your_stripe_publishable_key
STRIPE_SECRET_KEY=sk_test_your_stripe_secret_key
STRIPE_WEBHOOK_SECRET=whsec_your_webhook_secret
STRIPE_CURRENCY=usd

# PayPal 支付
PAYPAL_CLIENT_ID=your_paypal_client_id
PAYPAL_CLIENT_SECRET=your_paypal_client_secret
PAYPAL_MODE=sandbox
PAYPAL_CURRENCY=USD

# Apple Pay (通过 Stripe)
APPLE_PAY_DOMAIN=your-domain.com
APPLE_PAY_MERCHANT_ID=merchant.your.app.id

# Google Pay (通过 Stripe)
GOOGLE_PAY_MERCHANT_ID=your_google_pay_merchant_id
GOOGLE_PAY_MERCHANT_NAME=Your Store Name

# ===========================================
# 云存储配置 (可选)
# ===========================================
# Amazon S3
AWS_ACCESS_KEY_ID=your_aws_access_key_id
AWS_SECRET_ACCESS_KEY=your_aws_secret_access_key
AWS_S3_BUCKET=your_s3_bucket_name
AWS_S3_REGION=us-east-1
AWS_S3_ENDPOINT=https://s3.amazonaws.com

# Google Cloud Storage
GOOGLE_CLOUD_PROJECT_ID=your_project_id
GOOGLE_CLOUD_KEY_FILE=/path/to/service-account-key.json
GOOGLE_CLOUD_STORAGE_BUCKET=your_gcs_bucket_name

# Cloudflare R2
CLOUDFLARE_R2_ACCESS_KEY_ID=your_r2_access_key_id
CLOUDFLARE_R2_SECRET_ACCESS_KEY=your_r2_secret_access_key
CLOUDFLARE_R2_BUCKET=your_r2_bucket_name
CLOUDFLARE_R2_ENDPOINT=https://your-account-id.r2.cloudflarestorage.com

# ===========================================
# 监控和分析 (可选)
# ===========================================
# Google Analytics 4
NEXT_PUBLIC_GA_MEASUREMENT_ID=G-XXXXXXXXXX

# Google Tag Manager
NEXT_PUBLIC_GTM_ID=GTM-XXXXXXX

# Facebook Pixel
NEXT_PUBLIC_FACEBOOK_PIXEL_ID=your_facebook_pixel_id

# Hotjar
NEXT_PUBLIC_HOTJAR_ID=your_hotjar_id

# Sentry 错误监控
SENTRY_DSN=your_sentry_dsn
SENTRY_ORG=your_sentry_org
SENTRY_PROJECT=your_sentry_project

# Mixpanel 分析
MIXPANEL_TOKEN=your_mixpanel_token

# Amplitude 分析
AMPLITUDE_API_KEY=your_amplitude_api_key

# ===========================================
# 国际化和本地化配置
# ===========================================
# 默认语言和货币
DEFAULT_LOCALE=en-US
DEFAULT_CURRENCY=USD
DEFAULT_TIMEZONE=America/New_York

# 支持的语言
SUPPORTED_LOCALES=en-US,en-GB,fr-FR,de-DE,es-ES,it-IT,ja-JP,ko-KR

# 支持的货币
SUPPORTED_CURRENCIES=USD,EUR,GBP,CAD,AUD,JPY,KRW

# 汇率 API
EXCHANGE_RATE_API_KEY=your_exchange_rate_api_key
EXCHANGE_RATE_PROVIDER=fixer.io

# 物流配置
SHIPPING_CALCULATOR_API=shippo
SHIPPO_API_KEY=your_shippo_api_key

# 税务计算
TAX_CALCULATOR_API=taxjar
TAXJAR_API_KEY=your_taxjar_api_key

# ===========================================
# 开发环境配置
# ===========================================
# 是否启用调试模式
DEBUG=true

# 是否启用热重载
HOT_RELOAD=true

# API 请求超时时间 (毫秒)
API_TIMEOUT=30000

# ===========================================
# 生产环境配置
# ===========================================
# 域名配置
PRODUCTION_DOMAIN=your-domain.com
PRODUCTION_URL=https://your-domain.com

# SSL 证书路径
SSL_CERT_PATH=/etc/nginx/ssl/cert.pem
SSL_KEY_PATH=/etc/nginx/ssl/key.pem

# CDN 配置
CDN_URL=https://cdn.your-domain.com
