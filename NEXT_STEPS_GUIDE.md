# 🚀 插件安装完成 - 下一步配置指南

## ✅ 当前状态确认

7个核心插件已安装完成：
- ✅ WooCommerce
- ✅ WP GraphQL
- ✅ WPGraphQL for WooCommerce
- ✅ WP CORS
- ✅ JWT Authentication
- ✅ WooCommerce Stripe Gateway
- ✅ PayPal for WooCommerce

## 🎯 下一步操作清单

### 第一阶段: WooCommerce 基础配置 (15分钟)

#### 1. 完成 WooCommerce 设置向导
- 访问: http://localhost:8080/wp-admin/admin.php?page=wc-admin
- 如果出现设置向导，按以下信息填写:
  ```
  商店详情:
  - 地址: 123 Pet Street
  - 城市: New York
  - 州/省: NY
  - 邮编: 10001
  - 国家: United States (US)
  
  行业: 其他
  产品类型: 实体产品
  商店规模: 1-10
  目前销售: 否，我正在设置新商店
  ```

#### 2. 配置基础商店设置
- 进入: **WooCommerce → 设置 → 常规**
- 确认以下设置:
  ```
  货币: USD (美元)
  货币位置: 左侧 ($99.99)
  千位分隔符: ,
  小数分隔符: .
  小数位数: 2
  ```

#### 3. 配置税务设置 (可选)
- 进入: **WooCommerce → 设置 → 税务**
- 如果需要税务计算，启用税务
- 对于测试，可以暂时禁用

### 第二阶段: 支付网关配置 (20分钟)

#### 4. 配置 Stripe 支付 (测试模式)
- 进入: **WooCommerce → 设置 → 支付 → Stripe**
- 配置如下:
  ```
  启用 Stripe: ✅
  标题: Credit Card
  描述: Pay with your credit card via Stripe
  测试模式: ✅ 启用
  测试发布密钥: pk_test_... (需要 Stripe 账户)
  测试密钥: sk_test_... (需要 Stripe 账户)
  启用 Apple Pay: ✅
  启用 Google Pay: ✅
  ```

#### 5. 配置 PayPal 支付 (沙盒模式)
- 进入: **WooCommerce → 设置 → 支付 → PayPal**
- 配置如下:
  ```
  启用 PayPal: ✅
  标题: PayPal
  描述: Pay via PayPal
  沙盒模式: ✅ 启用
  沙盒客户端 ID: (需要 PayPal 开发者账户)
  沙盒客户端密钥: (需要 PayPal 开发者账户)
  ```

### 第三阶段: 产品分类和内容创建 (30分钟)

#### 6. 创建产品分类
- 进入: **产品 → 分类**
- 创建以下主要分类:
  ```
  🐕 Dogs (狗狗用品)
  ├── Dog Food (狗粮)
  ├── Dog Toys (狗玩具)
  ├── Dog Accessories (狗配件)
  └── Dog Healthcare (狗保健)
  
  🐱 Cats (猫咪用品)
  ├── Cat Food (猫粮)
  ├── Cat Toys (猫玩具)
  ├── Cat Litter (猫砂)
  └── Cat Healthcare (猫保健)
  
  🐦 Birds (鸟类用品)
  🐠 Fish (鱼类用品)
  🦎 Reptiles (爬虫用品)
  🐹 Small Pets (小宠物用品)
  ```

#### 7. 创建示例产品
- 进入: **产品 → 添加新产品**
- 创建 3-5 个示例产品，例如:
  ```
  产品1: Premium Dog Food
  - 价格: $29.99
  - 分类: Dogs → Dog Food
  - 库存: 100
  - 简短描述: 高质量狗粮，营养均衡
  
  产品2: Interactive Cat Toy
  - 价格: $15.99
  - 分类: Cats → Cat Toys
  - 库存: 50
  - 简短描述: 互动猫玩具，让猫咪更活跃
  ```

### 第四阶段: API 和前端集成配置 (15分钟)

#### 8. 验证 GraphQL 功能
- 访问: http://localhost:8080/graphql
- 应该看到 GraphQL 端点响应
- 测试查询示例:
  ```graphql
  query GetProducts {
    products {
      nodes {
        id
        name
        price
        slug
      }
    }
  }
  ```

#### 9. 配置 CORS 设置
- 进入: **设置 → WP CORS**
- 添加允许的域名:
  ```
  http://localhost:3000
  http://127.0.0.1:3000
  ```

#### 10. 配置 JWT 认证
- 在 wp-config.php 中添加 JWT 密钥 (已配置)
- 测试 JWT 端点: http://localhost:8080/wp-json/jwt-auth/v1/token

### 第五阶段: 前端服务检查 (10分钟)

#### 11. 检查 Next.js 前端状态
- 检查前端是否已启动: http://localhost:3000
- 如果还未启动，检查容器状态:
  ```bash
  docker logs pet-store-frontend
  ```

#### 12. 测试 API 连接
- 从前端测试 WordPress API 连接
- 验证 GraphQL 查询是否正常工作

## 🔧 快速配置脚本

### 创建示例产品分类 (可选)
```bash
# 如果 WP-CLI 可用，可以使用以下命令快速创建分类
docker exec -it pet-store-wordpress bash -c "
wp term create product_cat 'Dogs' --slug=dogs --allow-root
wp term create product_cat 'Cats' --slug=cats --allow-root
wp term create product_cat 'Birds' --slug=birds --allow-root
wp term create product_cat 'Fish' --slug=fish --allow-root
"
```

## 📱 重要链接

### WordPress 管理
- **WooCommerce 仪表板**: http://localhost:8080/wp-admin/admin.php?page=wc-admin
- **产品管理**: http://localhost:8080/wp-admin/edit.php?post_type=product
- **订单管理**: http://localhost:8080/wp-admin/edit.php?post_type=shop_order
- **WooCommerce 设置**: http://localhost:8080/wp-admin/admin.php?page=wc-settings

### API 端点
- **GraphQL**: http://localhost:8080/graphql
- **REST API**: http://localhost:8080/wp-json/wp/v2/
- **WooCommerce API**: http://localhost:8080/wp-json/wc/v3/
- **JWT 认证**: http://localhost:8080/wp-json/jwt-auth/v1/token

### 前端
- **Next.js 应用**: http://localhost:3000 (如果已启动)

## 🎯 优先级建议

### 立即完成 (必需)
1. ✅ WooCommerce 设置向导
2. ✅ 创建基础产品分类
3. ✅ 添加 2-3 个示例产品
4. ✅ 验证 GraphQL 功能

### 稍后配置 (推荐)
5. 配置支付网关 (需要 Stripe/PayPal 账户)
6. 设置运输选项
7. 配置邮件通知
8. 添加更多产品内容

### 可选配置
9. 税务设置
10. 优惠券系统
11. 库存管理规则
12. 高级产品选项

## 🚨 注意事项

### 支付网关配置
- **Stripe**: 需要注册 Stripe 账户获取 API 密钥
- **PayPal**: 需要 PayPal 开发者账户获取沙盒凭据
- 测试模式下可以使用测试卡号进行支付测试

### 前端集成
- 确保 Next.js 前端能够访问 WordPress API
- 验证 CORS 设置正确
- 测试 GraphQL 查询功能

## 📞 需要帮助？

如果在配置过程中遇到问题:
1. 检查插件是否正确激活
2. 查看 WordPress 错误日志
3. 验证 API 端点是否可访问
4. 确认前端容器是否正常运行

---

**完成这些配置后，你就拥有了一个功能完整的国际化宠物用品电商网站！** 🎉

下一步我们可以:
- 测试完整的购买流程
- 配置多语言支持
- 优化性能和SEO
- 部署到生产环境
