# 国际化功能指南

## 🌍 支持的国际化功能

### 多语言支持
- **英语** (en) - 默认语言
- **法语** (fr) - Français
- **德语** (de) - Deutsch
- **西班牙语** (es) - Español
- **意大利语** (it) - Italiano
- **日语** (ja) - 日本語
- **韩语** (ko) - 한국어

### 多货币支持
- **美元** (USD) - 默认货币
- **欧元** (EUR)
- **英镑** (GBP)
- **加拿大元** (CAD)
- **澳大利亚元** (AUD)
- **日元** (JPY)
- **韩元** (KRW)

### 支付方式
- **Stripe** - 信用卡支付（支持所有主要货币）
- **PayPal** - PayPal 账户支付
- **Apple Pay** - 苹果支付（通过 Stripe）
- **Google Pay** - 谷歌支付（通过 Stripe）

## 🔧 技术实现

### 前端国际化
使用 `next-intl` 库实现：

```typescript
// 语言配置
export const locales = ['en', 'fr', 'de', 'es', 'it', 'ja', 'ko'];
export const defaultLocale = 'en';

// 使用翻译
import { useTranslations } from 'next-intl';

function Component() {
  const t = useTranslations('common');
  return <h1>{t('welcome')}</h1>;
}
```

### 货币处理
使用 `currency.js` 进行精确计算：

```typescript
import { formatCurrency, convertCurrency } from '@/lib/currency';

// 格式化货币显示
const formatted = formatCurrency(99.99, 'USD', 'en-US');
// 输出: $99.99

// 货币转换
const converted = await convertCurrency(100, 'USD', 'EUR');
// 输出: 85.00 (基于实时汇率)
```

### 支付集成
支持多种国际支付方式：

```typescript
import { processPayment } from '@/lib/payment';

// Stripe 支付
const result = await processPayment('stripe', 99.99, 'USD', {
  orderId: '12345',
  customerId: 'cus_123',
});

// PayPal 支付
const result = await processPayment('paypal', 99.99, 'USD', {
  orderId: '12345',
});
```

## 🚀 配置指南

### 1. 环境变量配置

```bash
# 支付配置
STRIPE_PUBLISHABLE_KEY=pk_live_your_stripe_key
STRIPE_SECRET_KEY=sk_live_your_stripe_key
STRIPE_WEBHOOK_SECRET=whsec_your_webhook_secret

PAYPAL_CLIENT_ID=your_paypal_client_id
PAYPAL_CLIENT_SECRET=your_paypal_client_secret
PAYPAL_MODE=live  # 或 sandbox

# 国际化配置
DEFAULT_LOCALE=en-US
DEFAULT_CURRENCY=USD
SUPPORTED_LOCALES=en,fr,de,es,it,ja,ko
SUPPORTED_CURRENCIES=USD,EUR,GBP,CAD,AUD,JPY,KRW

# 汇率 API
EXCHANGE_RATE_API_KEY=your_api_key
EXCHANGE_RATE_PROVIDER=fixer  # 或 exchangerate, currencylayer
```

### 2. Stripe 配置

1. 注册 [Stripe 账户](https://stripe.com)
2. 获取 API 密钥
3. 配置 Webhook 端点：`https://your-domain.com/api/webhooks/stripe`
4. 启用所需的支付方式（Apple Pay, Google Pay）

### 3. PayPal 配置

1. 注册 [PayPal Developer 账户](https://developer.paypal.com)
2. 创建应用程序
3. 获取 Client ID 和 Client Secret
4. 配置 Webhook 端点：`https://your-domain.com/api/webhooks/paypal`

### 4. 汇率 API 配置

支持多个汇率提供商：

#### Fixer.io
```bash
EXCHANGE_RATE_PROVIDER=fixer
EXCHANGE_RATE_API_KEY=your_fixer_api_key
```

#### ExchangeRate-API (免费)
```bash
EXCHANGE_RATE_PROVIDER=exchangerate
# 无需 API 密钥
```

#### CurrencyLayer
```bash
EXCHANGE_RATE_PROVIDER=currencylayer
EXCHANGE_RATE_API_KEY=your_currencylayer_api_key
```

## 📱 用户体验

### 自动语言检测
系统会根据以下优先级自动选择语言：
1. 用户手动选择的语言（保存在 localStorage）
2. 浏览器语言设置
3. 默认语言（英语）

### 自动货币检测
系统会根据以下优先级自动选择货币：
1. 用户手动选择的货币（保存在 localStorage）
2. 基于语言的推断货币
3. 默认货币（美元）

### 实时汇率更新
- 汇率每小时自动更新
- 支持缓存机制，减少 API 调用
- 提供后备汇率，确保系统稳定性

## 🛠️ 开发指南

### 添加新语言

1. 在 `frontend/src/messages/` 目录下创建新的语言文件：
```bash
# 例如添加中文支持
cp frontend/src/messages/en.json frontend/src/messages/zh.json
```

2. 翻译 `zh.json` 中的所有文本

3. 更新语言配置：
```typescript
// frontend/src/lib/i18n.ts
export const locales = ['en', 'fr', 'de', 'es', 'it', 'ja', 'ko', 'zh'];

export const localeConfig = {
  // ... 其他语言配置
  zh: {
    name: '中文',
    flag: '🇨🇳',
    currency: 'CNY',
    currencySymbol: '¥',
    dateFormat: 'yyyy年MM月dd日',
    timeZone: 'Asia/Shanghai',
  },
};
```

### 添加新货币

1. 更新货币配置：
```typescript
// frontend/src/lib/i18n.ts
export const currencyConfig = {
  // ... 其他货币配置
  CNY: { symbol: '¥', decimals: 2, name: 'Chinese Yuan' },
};
```

2. 更新支付方式支持的货币列表：
```typescript
// frontend/src/lib/payment.ts
export const paymentMethods = {
  stripe: {
    // ...
    currencies: ['USD', 'EUR', 'GBP', 'CAD', 'AUD', 'JPY', 'CNY'],
  },
};
```

### 添加新支付方式

1. 在 `frontend/src/lib/payment.ts` 中添加新的支付方式配置
2. 实现支付处理函数
3. 创建相应的 API 路由
4. 更新前端支付组件

## 🧪 测试

### 测试支付功能
使用测试卡号进行 Stripe 测试：
- 成功支付：`4242 4242 4242 4242`
- 失败支付：`4000 0000 0000 0002`
- 需要验证：`4000 0025 0000 3155`

### 测试多货币
1. 切换到不同货币
2. 验证价格转换是否正确
3. 测试支付流程

### 测试多语言
1. 切换到不同语言
2. 验证所有文本是否正确翻译
3. 测试日期和数字格式

## 📊 监控和分析

### 支付监控
- 使用 Stripe Dashboard 监控支付状态
- 设置 Webhook 处理支付事件
- 记录支付失败原因

### 汇率监控
- 监控汇率 API 调用次数
- 设置汇率异常警报
- 记录汇率更新日志

### 用户行为分析
- 跟踪语言和货币选择
- 分析不同地区的转化率
- 监控国际用户的购买行为

## 🔒 安全考虑

### 支付安全
- 使用 HTTPS 进行所有支付通信
- 验证 Webhook 签名
- 不在前端存储敏感支付信息

### 数据保护
- 遵守 GDPR 和其他数据保护法规
- 加密存储用户偏好设置
- 定期审计安全配置

## 📞 支持和维护

### 常见问题
1. **支付失败** - 检查 API 密钥和 Webhook 配置
2. **汇率不更新** - 验证汇率 API 密钥和配额
3. **翻译缺失** - 检查语言文件完整性

### 维护任务
- 定期更新汇率
- 监控支付成功率
- 更新翻译文本
- 检查新的支付方式支持
