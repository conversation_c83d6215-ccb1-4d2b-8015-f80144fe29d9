#!/bin/bash

# 宠物用品电商网站启动脚本

set -e

echo "🐾 启动宠物用品电商网站..."

# 检查 Docker 和 Docker Compose 是否安装
if ! command -v docker &> /dev/null; then
    echo "❌ Docker 未安装，请先安装 Docker"
    exit 1
fi

# 检查 Docker Compose (优先使用新版本命令)
if docker compose version &> /dev/null; then
    DOCKER_COMPOSE_CMD="docker compose"
elif command -v docker-compose &> /dev/null; then
    DOCKER_COMPOSE_CMD="docker-compose"
else
    echo "❌ Docker Compose 未安装，请先安装 Docker Compose"
    exit 1
fi

echo "✅ 使用 Docker Compose 命令: $DOCKER_COMPOSE_CMD"

# 检查环境变量文件
if [ ! -f .env ]; then
    echo "📝 创建环境变量文件..."
    cp .env.example .env
    echo "⚠️  请编辑 .env 文件并填入正确的配置值"
    echo "   特别是数据库密码和安全密钥"
fi

# 创建必要的目录
echo "📁 创建必要的目录..."
mkdir -p backend/plugins backend/themes backend/uploads
mkdir -p nginx/logs
mkdir -p docker/mysql/data docker/redis/data

# 设置权限
echo "🔐 设置文件权限..."
chmod +x backend/init-wordpress.sh
chmod 644 .env

# 停止现有容器（如果存在）
echo "🛑 停止现有容器..."
$DOCKER_COMPOSE_CMD down --remove-orphans 2>/dev/null || true

# 构建和启动服务
echo "🚀 构建和启动服务..."
$DOCKER_COMPOSE_CMD up --build -d

# 等待服务启动
echo "⏳ 等待服务启动..."
sleep 30

# 检查服务状态
echo "🔍 检查服务状态..."
$DOCKER_COMPOSE_CMD ps

# 等待 WordPress 初始化完成
echo "⏳ 等待 WordPress 初始化完成..."
echo "   这可能需要几分钟时间..."

# 检查 WordPress 是否可访问
max_attempts=30
attempt=1
while [ $attempt -le $max_attempts ]; do
    if curl -s http://localhost:8080/wp-json/wp/v2/ > /dev/null 2>&1; then
        echo "✅ WordPress API 已就绪"
        break
    fi
    echo "   尝试 $attempt/$max_attempts - 等待 WordPress..."
    sleep 10
    attempt=$((attempt + 1))
done

if [ $attempt -gt $max_attempts ]; then
    echo "❌ WordPress 启动超时，请检查日志"
    $DOCKER_COMPOSE_CMD logs wordpress
    exit 1
fi

# 检查前端是否可访问
echo "🔍 检查前端服务..."
if curl -s http://localhost:3000 > /dev/null 2>&1; then
    echo "✅ 前端服务已就绪"
else
    echo "⚠️  前端服务可能还在启动中"
fi

echo ""
echo "🎉 宠物用品电商网站启动完成！"
echo ""
echo "📱 访问地址："
echo "   前端网站: http://localhost:3000"
echo "   WordPress 管理后台: http://localhost:8080/wp-admin"
echo "   数据库管理: http://localhost:8081 (phpMyAdmin)"
echo ""
echo "🔑 默认登录信息："
echo "   WordPress 用户名: admin"
echo "   WordPress 密码: admin123"
echo "   数据库用户名: wp_user"
echo "   数据库密码: wppassword123"
echo ""
echo "📋 常用命令："
echo "   查看日志: $DOCKER_COMPOSE_CMD logs -f"
echo "   停止服务: $DOCKER_COMPOSE_CMD down"
echo "   重启服务: $DOCKER_COMPOSE_CMD restart"
echo "   进入容器: $DOCKER_COMPOSE_CMD exec [service] bash"
echo ""
echo "⚠️  注意事项："
echo "   1. 首次启动可能需要较长时间来下载镜像和初始化数据库"
echo "   2. 请确保端口 3000、8080、8081、3306、6379 未被占用"
echo "   3. 生产环境请修改默认密码和安全配置"
echo ""
