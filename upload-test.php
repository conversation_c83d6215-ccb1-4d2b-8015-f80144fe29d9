<?php
/**
 * WordPress 上传功能测试页面
 */

// 包含 WordPress
require_once('/var/www/html/wp-config.php');
require_once('/var/www/html/wp-load.php');

echo "<h1>WordPress 上传功能测试</h1>";

// 检查上传目录
$upload_dir = wp_upload_dir();
echo "<h2>上传目录信息</h2>";
echo "<p><strong>上传目录:</strong> " . $upload_dir['basedir'] . "</p>";
echo "<p><strong>上传 URL:</strong> " . $upload_dir['baseurl'] . "</p>";
echo "<p><strong>当前子目录:</strong> " . $upload_dir['subdir'] . "</p>";
echo "<p><strong>完整路径:</strong> " . $upload_dir['path'] . "</p>";

// 检查目录权限
echo "<h2>目录权限检查</h2>";
$dirs_to_check = [
    $upload_dir['basedir'],
    $upload_dir['path'],
    '/var/www/html/wp-content',
    '/var/www/html/wp-content/uploads'
];

foreach ($dirs_to_check as $dir) {
    $exists = is_dir($dir);
    $writable = is_writable($dir);
    $perms = $exists ? substr(sprintf('%o', fileperms($dir)), -4) : 'N/A';
    
    echo "<p><strong>$dir:</strong> ";
    echo "存在: " . ($exists ? '✅' : '❌') . " | ";
    echo "可写: " . ($writable ? '✅' : '❌') . " | ";
    echo "权限: $perms</p>";
}

// 检查 PHP 配置
echo "<h2>PHP 上传配置</h2>";
$php_settings = [
    'upload_max_filesize' => ini_get('upload_max_filesize'),
    'post_max_size' => ini_get('post_max_size'),
    'max_execution_time' => ini_get('max_execution_time'),
    'memory_limit' => ini_get('memory_limit'),
    'file_uploads' => ini_get('file_uploads') ? '启用' : '禁用',
    'max_file_uploads' => ini_get('max_file_uploads')
];

foreach ($php_settings as $setting => $value) {
    echo "<p><strong>$setting:</strong> $value</p>";
}

// 测试文件创建
echo "<h2>文件创建测试</h2>";
$test_file = $upload_dir['path'] . '/upload-test-' . date('Y-m-d-H-i-s') . '.txt';
$test_content = "WordPress 上传测试文件\n创建时间: " . date('Y-m-d H:i:s');

if (file_put_contents($test_file, $test_content)) {
    echo "<p>✅ 测试文件创建成功: " . basename($test_file) . "</p>";
    echo "<p>文件路径: $test_file</p>";
    
    // 检查文件权限
    $file_perms = substr(sprintf('%o', fileperms($test_file)), -4);
    echo "<p>文件权限: $file_perms</p>";
    
    // 清理测试文件
    unlink($test_file);
    echo "<p>✅ 测试文件已清理</p>";
} else {
    echo "<p>❌ 测试文件创建失败</p>";
}

// WordPress 媒体库测试
echo "<h2>WordPress 媒体库状态</h2>";
$attachments = get_posts([
    'post_type' => 'attachment',
    'numberposts' => 5,
    'post_status' => 'inherit'
]);

echo "<p>媒体库中的文件数量: " . count($attachments) . "</p>";

if (!empty($attachments)) {
    echo "<h3>最近的媒体文件:</h3>";
    foreach ($attachments as $attachment) {
        $file_url = wp_get_attachment_url($attachment->ID);
        echo "<p>- " . $attachment->post_title . " (" . $file_url . ")</p>";
    }
}

echo "<h2>建议</h2>";
echo "<p>如果上传仍然失败，请检查:</p>";
echo "<ul>";
echo "<li>确保 wp-content/uploads 目录权限为 775</li>";
echo "<li>确保 www-data 用户拥有上传目录</li>";
echo "<li>检查 PHP 错误日志</li>";
echo "<li>验证 WordPress 配置中的 WP_CONTENT_DIR 设置</li>";
echo "</ul>";

echo "<p><a href='/wp-admin/upload.php'>返回媒体库</a></p>";
?>
