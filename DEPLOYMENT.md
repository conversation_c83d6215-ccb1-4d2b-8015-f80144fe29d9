# 部署指南

## 项目架构

```
宠物用品电商网站
├── 前端 (Next.js 15)
│   ├── TypeScript + React 18
│   ├── Tailwind CSS + Shadcn/ui
│   ├── TanStack Query + Zustand
│   └── NextAuth.js
├── 后端 (WordPress 6.6)
│   ├── WooCommerce
│   ├── WP GraphQL
│   ├── Advanced Custom Fields
│   └── Redis 缓存
├── 数据库 (MySQL 8.0)
├── 缓存 (Redis 7)
└── 反向代理 (Nginx)
```

## 技术特性

### 🚀 2025年最新技术栈
- **Next.js 15**: 最新的 React 全栈框架，支持 App Router
- **WordPress 6.6**: 最新版本，作为 Headless CMS
- **PHP 8.3**: 最新 PHP 版本，性能优化
- **MySQL 8.0**: 最新数据库版本
- **Redis 7**: 最新缓存系统
- **Docker**: 容器化部署

### 🎯 电商功能
- 产品展示和分类
- 购物车和结算
- 用户注册和登录
- 订单管理
- 支付集成（Stripe、PayPal、Apple Pay、Google Pay）
- 库存管理
- 优惠券系统
- 多币种支持
- 国际物流集成

### 🔧 开发特性
- TypeScript 类型安全
- 热重载开发环境
- GraphQL API
- REST API 支持
- Redis 缓存优化
- SEO 优化
- 响应式设计

## 快速开始

### 1. 环境要求
- Docker 20.10+
- Docker Compose 2.0+
- 至少 4GB 可用内存
- 至少 10GB 可用磁盘空间

### 2. 启动项目
```bash
# 克隆项目
git clone <repository-url>
cd pet-ecommerce-store

# 启动所有服务
./start.sh
```

### 3. 访问应用
- 前端: http://localhost:3000
- WordPress 管理: http://localhost:8080/wp-admin
- 数据库管理: http://localhost:8081

## 开发环境

### 目录结构
```
pet-ecommerce-store/
├── frontend/                 # Next.js 前端
│   ├── src/
│   │   ├── app/              # App Router 页面
│   │   ├── components/       # React 组件
│   │   ├── lib/              # 工具库
│   │   ├── hooks/            # 自定义 Hooks
│   │   ├── types/            # TypeScript 类型
│   │   ├── utils/            # 工具函数
│   │   └── store/            # 状态管理
│   ├── package.json
│   ├── next.config.js
│   ├── tailwind.config.js
│   └── Dockerfile
├── backend/                  # WordPress 后端
│   ├── plugins/              # 自定义插件
│   ├── themes/               # 自定义主题
│   ├── uploads/              # 上传文件
│   ├── wp-config.php
│   ├── init-wordpress.sh
│   └── Dockerfile
├── docker/                   # Docker 配置
│   ├── mysql/
│   └── redis/
├── nginx/                    # Nginx 配置
│   ├── nginx.conf
│   └── conf.d/
├── docker-compose.yml        # 开发环境
├── docker-compose.prod.yml   # 生产环境
├── .env                      # 环境变量
└── README.md
```

### 开发命令
```bash
# 查看服务状态
docker-compose ps

# 查看日志
docker-compose logs -f [service]

# 重启服务
docker-compose restart [service]

# 进入容器
docker-compose exec [service] bash

# 停止所有服务
./stop.sh
```

## 生产环境部署

### 1. 准备生产环境
```bash
# 复制生产环境配置
cp .env.example .env.prod

# 编辑生产环境变量
nano .env.prod
```

### 2. 重要配置项
```bash
# 数据库安全
MYSQL_ROOT_PASSWORD=strong_password_here
MYSQL_PASSWORD=strong_password_here

# WordPress 安全密钥
# 从 https://api.wordpress.org/secret-key/1.1/salt/ 获取

# 域名配置
PRODUCTION_DOMAIN=your-domain.com
PRODUCTION_URL=https://your-domain.com

# SSL 证书
SSL_CERT_PATH=/etc/nginx/ssl/cert.pem
SSL_KEY_PATH=/etc/nginx/ssl/key.pem
```

### 3. 启动生产环境
```bash
# 使用生产配置启动
docker-compose -f docker-compose.prod.yml --env-file .env.prod up -d
```

### 4. SSL 证书配置
```bash
# 使用 Let's Encrypt
certbot --nginx -d your-domain.com

# 或者上传自己的证书到 nginx/ssl/ 目录
```

## 性能优化

### 1. 数据库优化
- 启用 MySQL 查询缓存
- 配置 InnoDB 缓冲池
- 定期优化数据库表

### 2. 缓存优化
- Redis 对象缓存
- Nginx 静态文件缓存
- CDN 配置

### 3. 前端优化
- Next.js 静态生成
- 图片优化
- 代码分割

## 监控和维护

### 1. 日志管理
```bash
# 查看应用日志
docker-compose logs -f

# 查看 Nginx 日志
docker-compose exec nginx tail -f /var/log/nginx/access.log
```

### 2. 数据备份
```bash
# 备份数据库
docker-compose exec mysql mysqldump -u root -p wordpress > backup.sql

# 备份文件
tar -czf uploads-backup.tar.gz backend/uploads/
```

### 3. 更新维护
```bash
# 更新镜像
docker-compose pull

# 重新构建
docker-compose up --build -d
```

## 故障排除

### 常见问题

1. **端口冲突**
   - 检查端口 3000, 8080, 8081, 3306, 6379 是否被占用
   - 修改 docker-compose.yml 中的端口映射

2. **内存不足**
   - 确保至少有 4GB 可用内存
   - 调整 Docker 内存限制

3. **权限问题**
   - 检查文件权限：`chmod +x start.sh`
   - 检查目录权限：`chown -R $USER:$USER .`

4. **数据库连接失败**
   - 检查环境变量配置
   - 等待数据库完全启动

### 获取帮助
- 查看日志：`docker-compose logs [service]`
- 检查容器状态：`docker-compose ps`
- 重启服务：`docker-compose restart [service]`
