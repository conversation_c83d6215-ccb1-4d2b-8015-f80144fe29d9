version: '3.8'

services:
  # MySQL 数据库
  mysql:
    image: mysql:8.0
    container_name: pet-store-mysql
    restart: unless-stopped
    environment:
      MYSQL_ROOT_PASSWORD: ${MYSQL_ROOT_PASSWORD}
      MYSQL_DATABASE: ${MYSQL_DATABASE}
      MYSQL_USER: ${MYSQL_USER}
      MYSQL_PASSWORD: ${MYSQL_PASSWORD}
    volumes:
      - mysql_data:/var/lib/mysql
      - ./docker/mysql/init:/docker-entrypoint-initdb.d
    ports:
      - "3306:3306"
    networks:
      - pet-store-network
    command: --default-authentication-plugin=mysql_native_password

  # Redis 缓存
  redis:
    image: redis:7-alpine
    container_name: pet-store-redis
    restart: unless-stopped
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - pet-store-network
    command: redis-server --appendonly yes

  # WordPress 后端 (Headless CMS)
  wordpress:
    build:
      context: ./backend
      dockerfile: Dockerfile
    container_name: pet-store-wordpress
    restart: unless-stopped
    environment:
      WORDPRESS_DB_HOST: mysql:3306
      WORDPRESS_DB_NAME: ${MYSQL_DATABASE}
      WORDPRESS_DB_USER: ${MYSQL_USER}
      WORDPRESS_DB_PASSWORD: ${MYSQL_PASSWORD}
      WORDPRESS_TABLE_PREFIX: wp_
      WORDPRESS_DEBUG: ${WORDPRESS_DEBUG:-false}
      WP_REDIS_HOST: redis
      WP_REDIS_PORT: 6379
    volumes:
      - wordpress_data:/var/www/html
      - ./backend/themes:/var/www/html/wp-content/themes/custom
      - ./backend/plugins:/var/www/html/wp-content/plugins/custom
      - ./backend/uploads:/var/www/html/wp-content/uploads
    ports:
      - "8080:80"
    depends_on:
      - mysql
      - redis
    networks:
      - pet-store-network

  # Next.js 前端
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
      target: development
    container_name: pet-store-frontend
    restart: unless-stopped
    environment:
      NODE_ENV: development
      NEXT_PUBLIC_WORDPRESS_URL: http://wordpress
      NEXT_PUBLIC_API_URL: ${NEXT_PUBLIC_API_URL}
      NEXTAUTH_SECRET: ${NEXTAUTH_SECRET}
      NEXTAUTH_URL: ${NEXTAUTH_URL}
    volumes:
      - ./frontend:/app
      - /app/node_modules
      - /app/.next
    ports:
      - "3000:3000"
    depends_on:
      - wordpress
    networks:
      - pet-store-network
    command: npm run dev

  # Nginx 反向代理
  nginx:
    image: nginx:alpine
    container_name: pet-store-nginx
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf
      - ./nginx/conf.d:/etc/nginx/conf.d
      - ./nginx/ssl:/etc/nginx/ssl
    depends_on:
      - frontend
      - wordpress
    networks:
      - pet-store-network

  # phpMyAdmin (开发环境)
  phpmyadmin:
    image: phpmyadmin/phpmyadmin:latest
    container_name: pet-store-phpmyadmin
    restart: unless-stopped
    environment:
      PMA_HOST: mysql
      PMA_PORT: 3306
      PMA_USER: ${MYSQL_USER}
      PMA_PASSWORD: ${MYSQL_PASSWORD}
    ports:
      - "8081:80"
    depends_on:
      - mysql
    networks:
      - pet-store-network

volumes:
  mysql_data:
  redis_data:
  wordpress_data:

networks:
  pet-store-network:
    driver: bridge
