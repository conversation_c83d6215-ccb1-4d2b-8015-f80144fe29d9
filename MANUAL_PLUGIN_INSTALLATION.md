# 🔌 手动安装 7 个核心插件指南

由于 WP-CLI 安装遇到问题，我们将通过 WordPress 管理界面手动安装这 7 个核心插件。

## 📋 需要安装的 7 个核心插件

### 1. **WooCommerce** ⭐⭐⭐⭐⭐
- **作用**: 电商核心功能
- **搜索关键词**: `WooCommerce`
- **开发者**: Automattic

### 2. **WP GraphQL** ⭐⭐⭐⭐⭐
- **作用**: GraphQL API (Next.js 数据获取)
- **搜索关键词**: `WPGraphQL`
- **开发者**: WPGraphQL

### 3. **WPGraphQL for WooCommerce** ⭐⭐⭐⭐⭐
- **作用**: WooCommerce GraphQL 支持
- **搜索关键词**: `WPGraphQL WooCommerce`
- **开发者**: WPGraphQL

### 4. **WP CORS** ⭐⭐⭐⭐⭐
- **作用**: 跨域请求支持
- **搜索关键词**: `WP CORS`
- **开发者**: Various

### 5. **JWT Authentication for WP REST API** ⭐⭐⭐⭐⭐
- **作用**: 用户认证和会话管理
- **搜索关键词**: `JWT Authentication`
- **开发者**: Enrique Chavez

### 6. **WooCommerce Stripe Gateway** ⭐⭐⭐⭐⭐
- **作用**: 信用卡支付 (含 Apple Pay, Google Pay)
- **搜索关键词**: `WooCommerce Stripe`
- **开发者**: WooCommerce

### 7. **PayPal for WooCommerce** ⭐⭐⭐⭐⭐
- **作用**: PayPal 支付集成
- **搜索关键词**: `PayPal WooCommerce`
- **开发者**: WooCommerce

## 🚀 安装步骤

### 第一步: 访问 WordPress 管理后台
1. 打开浏览器访问: http://localhost:8080/wp-admin
2. 使用以下凭据登录:
   - **用户名**: `admin`
   - **密码**: `admin123`

### 第二步: 进入插件安装页面
1. 在左侧菜单中点击 **"插件"**
2. 点击 **"安装插件"**

### 第三步: 逐个安装插件

#### 安装 WooCommerce
1. 在搜索框中输入: `WooCommerce`
2. 找到 **WooCommerce** (by Automattic)
3. 点击 **"现在安装"**
4. 安装完成后点击 **"启用"**
5. ✅ 完成第 1 个插件

#### 安装 WP GraphQL
1. 在搜索框中输入: `WPGraphQL`
2. 找到 **WPGraphQL** (by WPGraphQL)
3. 点击 **"现在安装"**
4. 安装完成后点击 **"启用"**
5. ✅ 完成第 2 个插件

#### 安装 WPGraphQL for WooCommerce
1. 在搜索框中输入: `WPGraphQL WooCommerce`
2. 找到 **WPGraphQL WooCommerce** (by WPGraphQL, Geoff Taylor)
3. 点击 **"现在安装"**
4. 安装完成后点击 **"启用"**
5. ✅ 完成第 3 个插件

#### 安装 WP CORS
1. 在搜索框中输入: `WP CORS`
2. 找到 **WP CORS** 插件
3. 点击 **"现在安装"**
4. 安装完成后点击 **"启用"**
5. ✅ 完成第 4 个插件

#### 安装 JWT Authentication
1. 在搜索框中输入: `JWT Authentication`
2. 找到 **JWT Authentication for WP REST API** (by Enrique Chavez)
3. 点击 **"现在安装"**
4. 安装完成后点击 **"启用"**
5. ✅ 完成第 5 个插件

#### 安装 WooCommerce Stripe Gateway
1. 在搜索框中输入: `WooCommerce Stripe`
2. 找到 **WooCommerce Stripe Gateway** (by WooCommerce)
3. 点击 **"现在安装"**
4. 安装完成后点击 **"启用"**
5. ✅ 完成第 6 个插件

#### 安装 PayPal for WooCommerce
1. 在搜索框中输入: `PayPal WooCommerce`
2. 找到 **PayPal for WooCommerce** (by WooCommerce)
3. 点击 **"现在安装"**
4. 安装完成后点击 **"启用"**
5. ✅ 完成第 7 个插件

## ✅ 安装完成验证

### 检查已安装的插件
1. 在左侧菜单中点击 **"插件"** → **"已安装的插件"**
2. 确认以下 7 个插件都显示为 **"已启用"**:
   - ✅ WooCommerce
   - ✅ WPGraphQL
   - ✅ WPGraphQL WooCommerce
   - ✅ WP CORS
   - ✅ JWT Authentication for WP REST API
   - ✅ WooCommerce Stripe Gateway
   - ✅ PayPal for WooCommerce

### 验证功能
1. **WooCommerce**: 左侧菜单应该出现 **"WooCommerce"** 选项
2. **GraphQL**: 访问 http://localhost:8080/graphql 应该显示 GraphQL 端点
3. **API**: 访问 http://localhost:8080/wp-json/wp/v2/ 应该显示 REST API

## 🔧 基础配置

### 配置 WooCommerce
1. 点击左侧菜单的 **"WooCommerce"**
2. 如果出现设置向导，按以下信息填写:
   - **商店地址**: 123 Pet Street
   - **城市**: New York
   - **州/省**: NY
   - **邮编**: 10001
   - **国家**: United States
   - **货币**: USD (美元)
   - **产品类型**: 实体和数字产品

### 配置支付网关
1. 进入 **WooCommerce** → **设置** → **支付**
2. 配置 **Stripe**:
   - 启用 Stripe
   - 设置为测试模式
   - 添加测试 API 密钥 (稍后配置)
3. 配置 **PayPal**:
   - 启用 PayPal
   - 设置为沙盒模式
   - 添加沙盒凭据 (稍后配置)

## 🎯 下一步操作

安装完成后，你可以:

1. **创建产品分类**:
   - 狗狗用品 (Dogs)
   - 猫咪用品 (Cats)
   - 鸟类用品 (Birds)
   - 鱼类用品 (Fish)

2. **添加示例产品**:
   - 狗粮、猫粮
   - 玩具
   - 配件

3. **测试 API 功能**:
   - GraphQL 查询
   - REST API 调用
   - 用户认证

4. **配置支付测试**:
   - Stripe 测试卡号
   - PayPal 沙盒账户

## 💡 提示

- 每个插件安装大约需要 1-2 分钟
- 总安装时间约 10-15 分钟
- 如果某个插件找不到，可以尝试不同的搜索关键词
- 安装过程中如果出现错误，可以刷新页面重试

## 📞 需要帮助？

如果在安装过程中遇到问题:
1. 检查网络连接
2. 确认 WordPress 管理员权限
3. 查看 WordPress 错误日志
4. 尝试逐个安装而不是批量安装

---

**完成这 7 个插件的安装后，你就拥有了一个功能完整的 Next.js + WordPress 电商网站后端！** 🎉
