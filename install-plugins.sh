#!/bin/bash

# WordPress 插件自动安装脚本

echo "🔌 开始安装 WordPress 插件..."

# 进入 WordPress 容器并安装插件
docker exec -it pet-store-wordpress bash -c "
cd /var/www/html

# 下载并安装 WP-CLI
if [ ! -f /usr/local/bin/wp ]; then
    echo '下载 WP-CLI...'
    curl -L https://github.com/wp-cli/wp-cli/releases/download/v2.10.0/wp-cli-2.10.0.phar -o wp-cli.phar
    chmod +x wp-cli.phar
    mv wp-cli.phar /usr/local/bin/wp
fi

echo '验证 WP-CLI 安装...'
wp --version --allow-root

echo '开始安装插件...'

# 安装 WooCommerce
echo '安装 WooCommerce...'
wp plugin install woocommerce --activate --allow-root

# 安装 WP GraphQL
echo '安装 WP GraphQL...'
wp plugin install wp-graphql --activate --allow-root

# 安装 Advanced Custom Fields
echo '安装 Advanced Custom Fields...'
wp plugin install advanced-custom-fields --activate --allow-root

# 安装 Redis Cache
echo '安装 Redis Object Cache...'
wp plugin install redis-cache --activate --allow-root

# 安装 Stripe 支付网关
echo '安装 Stripe Payment Gateway...'
wp plugin install woocommerce-gateway-stripe --activate --allow-root

# 安装 CORS 支持
echo '安装 CORS 支持...'
wp plugin install wp-cors --activate --allow-root

# 设置永久链接结构
echo '设置永久链接...'
wp rewrite structure '/%postname%/' --allow-root
wp rewrite flush --allow-root

# 创建基础页面
echo '创建基础页面...'
wp post create --post_type=page --post_title='Home' --post_status=publish --allow-root
wp post create --post_type=page --post_title='Shop' --post_status=publish --allow-root
wp post create --post_type=page --post_title='About Us' --post_status=publish --allow-root
wp post create --post_type=page --post_title='Contact' --post_status=publish --allow-root

# 配置 WooCommerce 基础设置
echo '配置 WooCommerce...'
wp option update woocommerce_store_address '123 Pet Street' --allow-root
wp option update woocommerce_store_city 'New York' --allow-root
wp option update woocommerce_default_country 'US:NY' --allow-root
wp option update woocommerce_store_postcode '10001' --allow-root
wp option update woocommerce_currency 'USD' --allow-root
wp option update woocommerce_product_type 'both' --allow-root
wp option update woocommerce_allow_tracking 'no' --allow-root

# 配置货币设置
wp option update woocommerce_currency_pos 'left' --allow-root
wp option update woocommerce_price_thousand_sep ',' --allow-root
wp option update woocommerce_price_decimal_sep '.' --allow-root
wp option update woocommerce_price_num_decimals '2' --allow-root

# 启用 Redis 缓存
echo '启用 Redis 缓存...'
wp redis enable --allow-root

echo '插件安装完成!'
echo '管理后台: http://localhost:8080/wp-admin'
echo '用户名: admin'
echo '密码: admin123'
"
