<?php
/**
 * WordPress 配置文件 - 开发环境
 */

// ** MySQL 数据库配置 ** //
define('DB_NAME', getenv('WORDPRESS_DB_NAME'));
define('DB_USER', getenv('WORDPRESS_DB_USER'));
define('DB_PASSWORD', getenv('WORDPRESS_DB_PASSWORD'));
define('DB_HOST', getenv('WORDPRESS_DB_HOST'));
define('DB_CHARSET', 'utf8mb4');
define('DB_COLLATE', '');

// ** 认证密钥和盐值 ** //
define('AUTH_KEY',         'put your unique phrase here');
define('SECURE_AUTH_KEY',  'put your unique phrase here');
define('LOGGED_IN_KEY',    'put your unique phrase here');
define('NONCE_KEY',        'put your unique phrase here');
define('AUTH_SALT',        'put your unique phrase here');
define('SECURE_AUTH_SALT', 'put your unique phrase here');
define('LOGGED_IN_SALT',   'put your unique phrase here');
define('NONCE_SALT',       'put your unique phrase here');

// ** 数据库表前缀 ** //
$table_prefix = getenv('WORDPRESS_TABLE_PREFIX') ?: 'wp_';

// ** 调试模式 ** //
define('WP_DEBUG', getenv('WORDPRESS_DEBUG') === 'true');
define('WP_DEBUG_LOG', true);
define('WP_DEBUG_DISPLAY', false);
define('SCRIPT_DEBUG', true);

// ** WordPress 地址配置 ** //
define('WP_HOME', 'http://localhost:8080');
define('WP_SITEURL', 'http://localhost:8080');

// ** 文件系统配置 ** //
define('FS_METHOD', 'direct');
define('WP_MEMORY_LIMIT', '512M');

// ** 多站点配置 ** //
define('WP_ALLOW_MULTISITE', false);

// ** 自动更新配置 ** //
define('AUTOMATIC_UPDATER_DISABLED', true);
define('WP_AUTO_UPDATE_CORE', false);

// ** 文件编辑配置 ** //
define('DISALLOW_FILE_EDIT', false);
define('DISALLOW_FILE_MODS', false);

// ** 缓存配置 ** //
define('WP_CACHE', true);
define('WP_CACHE_KEY_SALT', 'pet-store-cache');

// ** Redis 配置 ** //
define('WP_REDIS_HOST', getenv('WP_REDIS_HOST') ?: 'redis');
define('WP_REDIS_PORT', getenv('WP_REDIS_PORT') ?: 6379);
define('WP_REDIS_DATABASE', 0);
define('WP_REDIS_TIMEOUT', 1);
define('WP_REDIS_READ_TIMEOUT', 1);

// ** CORS 配置 ** //
define('CORS_ALLOW_ORIGIN', 'http://localhost:3000');

// ** GraphQL 配置 ** //
define('GRAPHQL_DEBUG', true);

// ** WooCommerce 配置 ** //
define('WC_ADMIN_DISABLED', false);

// ** 上传配置 ** //
define('UPLOADS', 'wp-content/uploads');

// ** SSL 配置 ** //
if (isset($_SERVER['HTTP_X_FORWARDED_PROTO']) && $_SERVER['HTTP_X_FORWARDED_PROTO'] === 'https') {
    $_SERVER['HTTPS'] = 'on';
    define('FORCE_SSL_ADMIN', true);
}

// ** 自定义常量 ** //
define('HEADLESS_MODE_CLIENT_URL', 'http://localhost:3000');
define('JWT_AUTH_SECRET_KEY', 'your-secret-key-here');
define('JWT_AUTH_CORS_ENABLE', true);

// ** 加载 WordPress ** //
if (!defined('ABSPATH')) {
    define('ABSPATH', __DIR__ . '/');
}

require_once ABSPATH . 'wp-settings.php';
