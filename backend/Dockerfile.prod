# WordPress Production Dockerfile

FROM wordpress:6.6-php8.3-apache

# 安装必要的 PHP 扩展和工具
RUN apt-get update && apt-get install -y \
    libzip-dev \
    zip \
    unzip \
    git \
    curl \
    libpng-dev \
    libjpeg-dev \
    libfreetype6-dev \
    libwebp-dev \
    libavif-dev \
    && docker-php-ext-configure gd --with-freetype --with-jpeg --with-webp --with-avif \
    && docker-php-ext-install -j$(nproc) gd zip mysqli pdo pdo_mysql opcache \
    && apt-get clean \
    && rm -rf /var/lib/apt/lists/*

# 安装 Redis PHP 扩展
RUN pecl install redis && docker-php-ext-enable redis

# 生产环境 PHP 配置
RUN { \
    echo 'memory_limit = 256M'; \
    echo 'upload_max_filesize = 32M'; \
    echo 'post_max_size = 32M'; \
    echo 'max_execution_time = 120'; \
    echo 'max_input_vars = 3000'; \
    echo 'opcache.enable = 1'; \
    echo 'opcache.memory_consumption = 256'; \
    echo 'opcache.interned_strings_buffer = 16'; \
    echo 'opcache.max_accelerated_files = 10000'; \
    echo 'opcache.revalidate_freq = 0'; \
    echo 'opcache.validate_timestamps = 0'; \
    echo 'opcache.fast_shutdown = 1'; \
    echo 'opcache.enable_file_override = 1'; \
    echo 'realpath_cache_size = 4096K'; \
    echo 'realpath_cache_ttl = 600'; \
    echo 'expose_php = Off'; \
    echo 'display_errors = Off'; \
    echo 'log_errors = On'; \
    echo 'error_log = /var/log/php_errors.log'; \
} > /usr/local/etc/php/conf.d/production.ini

# 启用 Apache 模块
RUN a2enmod rewrite headers expires deflate ssl

# 复制生产环境 Apache 配置
COPY apache-config.prod.conf /etc/apache2/sites-available/000-default.conf

# 复制 WordPress 配置
COPY wp-config.prod.php /var/www/html/wp-config.php

# 复制自定义插件和主题
COPY plugins/ /var/www/html/wp-content/plugins/
COPY themes/ /var/www/html/wp-content/themes/

# 复制生产环境初始化脚本
COPY init-wordpress.prod.sh /usr/local/bin/
RUN chmod +x /usr/local/bin/init-wordpress.prod.sh

# 设置严格的权限
RUN chown -R www-data:www-data /var/www/html \
    && find /var/www/html -type d -exec chmod 750 {} \; \
    && find /var/www/html -type f -exec chmod 640 {} \; \
    && chmod 600 /var/www/html/wp-config.php

# 创建日志目录
RUN mkdir -p /var/log/wordpress && chown www-data:www-data /var/log/wordpress

# 健康检查
HEALTHCHECK --interval=30s --timeout=10s --start-period=60s --retries=3 \
    CMD curl -f http://localhost/wp-json/wp/v2/ || exit 1

# 启动脚本
ENTRYPOINT ["/usr/local/bin/init-wordpress.prod.sh"]
CMD ["apache2-foreground"]
