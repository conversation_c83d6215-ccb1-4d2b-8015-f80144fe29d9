2025-08-04T04:32:40+00:00 DEBUG #4280 - [New Request] POST /wp-json/wc-admin/plugins/install
2025-08-04T04:32:40+00:00 DEBUG #4280 - GET https://api.woocommerce.com/integrations/ppc/v1/notifications/webhooks
Response: Array
(
    [code] => 404
    [message] => Not Found
)

Response Body: {"statusCode":404,"error":"Not Found","message":"Not Found"}
2025-08-04T04:32:40+00:00 ERROR #4280 - Failed to delete webhooks: [Error] Not Found
2025-08-04T04:32:40+00:00 INFO #4280 - Webhooks deleted.
2025-08-04T04:32:41+00:00 DEBUG #4280 - POST https://api.woocommerce.com/integrations/ppc/v1/notifications/webhooks
Request Body: {"url":"https:\/\/localhost:8080\/wp-json\/paypal\/v1\/incoming","event_types":[{"name":"CHECKOUT.ORDER.APPROVED"},{"name":"CHECKOUT.ORDER.COMPLETED"},{"name":"CHECKOUT.PAYMENT-APPROVAL.REVERSED"},{"name":"PAYMENT.CAPTURE.REFUNDED"},{"name":"PAYMENT.AUTHORIZATION.VOIDED"},{"name":"PAYMENT.CAPTURE.REVERSED"},{"name":"PAYMENT.ORDER.CANCELLED"},{"name":"PAYMENT.CAPTURE.DENIED"},{"name":"PAYMENT.CAPTURE.COMPLETED"},{"name":"VAULT.PAYMENT-TOKEN.CREATED"},{"name":"VAULT.PAYMENT-TOKEN.DELETED"},{"name":"PAYMENT.CAPTURE.PENDING"},{"name":"PAYMENT.SALE.COMPLETED"},{"name":"PAYMENT.SALE.REFUNDED"},{"name":"BILLING.SUBSCRIPTION.CANCELLED"},{"name":"BILLING.PLAN.PRICING-CHANGE.ACTIVATED"},{"name":"CATALOG.PRODUCT.UPDATED"},{"name":"BILLING.PLAN.UPDATED"}]}
Response: Array
(
    [code] => 404
    [message] => Not Found
)

Response Body: {"statusCode":404,"error":"Not Found","message":"Not Found"}
2025-08-04T04:32:41+00:00 ERROR #4280 - Failed to subscribe webhooks: [Error] Not Found
2025-08-04T09:06:09+00:00 DEBUG #6869 - [New Request] GET /wp-admin/admin.php
2025-08-04T09:06:09+00:00 DEBUG #6869 - GET https://api.woocommerce.com/integrations/ppc/v1/customer/partners/K8SKZ36LQBWXJ/merchant-integrations/
Response: Array
(
    [code] => 404
    [message] => Not Found
)

Response Body: {"statusCode":404,"error":"Not Found","message":"Not Found"}
2025-08-04T09:06:09+00:00 WARNING #6869 - [Error] Not Found CONTEXT: {"args":{"method":"GET","headers":{"Authorization":"Bearer token","Content-Type":"application/json"}},"response":{"headers":{},"body":"{"statusCode":404,"error":"Not Found","message":"Not Found"}","response":{"code":404,"message":"Not Found"},"cookies":[],"filename":null,"http_response":{"data":null,"headers":null,"status":null}}}
2025-08-04T09:06:11+00:00 DEBUG #4449 - [New Request] GET /wp-json/wc/v3/wc_paypal/webhooks
2025-08-04T09:06:11+00:00 DEBUG #4449 - GET https://api.woocommerce.com/integrations/ppc/v1/notifications/webhooks
Response: Array
(
    [code] => 404
    [message] => Not Found
)

Response Body: {"statusCode":404,"error":"Not Found","message":"Not Found"}
2025-08-04T09:06:51+00:00 DEBUG #6909 - [New Request] POST /wp-json/wc/v3/wc_paypal/login_link
2025-08-04T09:06:51+00:00 INFO #6909 - Generating onboarding URL for: production-advanced_vaulting-express_checkout-
2025-08-04T09:06:53+00:00 DEBUG #6909 - POST https://api.woocommerce.com/integrations/ppc/v2/customer/partner-referrals
Request Body: {"partner_config_override":{"return_url":"http:\/\/localhost:8080\/wp-admin\/admin.php?page=wc-settings&tab=checkout&section=ppcp-gateway&ppcpToken=eyJrIjoicHJvZHVjdGlvbi1hZHZhbmNlZF92YXVsdGluZy1leHByZXNzX2NoZWNrb3V0LSIsInUiOjEsImgiOiJmMGUxOGRmMGYxZjhlZmU1YzhmNTMwYmVjZjQxYzllNyJ9","return_url_description":"Return to your shop.","show_add_credit_card":false},"products":["PPCP","ADVANCED_VAULTING"],"capabilities":["PAYPAL_WALLET_VAULTING_ADVANCED"],"legal_consents":[{"type":"SHARE_DATA_CONSENT","granted":true}],"operations":[{"operation":"API_INTEGRATION","api_integration_preference":{"rest_api_integration":{"integration_method":"PAYPAL","integration_type":"FIRST_PARTY","first_party_details":{"features":["PAYMENT","REFUND","ADVANCED_TRANSACTIONS_SEARCH","TRACKING_SHIPMENT_READWRITE","BILLING_AGREEMENT"],"seller_nonce":"a1233wtergfsdt4365tzrshgfbaewa36AGa1233wtergfsdt4365tzrshgfbaewa36AG"}}}}]}
Response Debug ID: 8df3359a9553b
Response: Array
(
    [code] => 201
    [message] => Created
)

2025-08-04T09:06:55+00:00 DEBUG #1902 - [New Request] POST /wp-json/wc/v3/wc_paypal/login_link
2025-08-04T09:06:55+00:00 DEBUG #1902 - Loaded onboarding URL from cache: production-advanced_vaulting-express_checkout-
2025-08-04T09:06:55+00:00 INFO #1902 - Using cached onboarding URL for: production-advanced_vaulting-express_checkout-
2025-08-04T09:06:55+00:00 DEBUG #2989 - [New Request] POST /wp-json/wc/v3/wc_paypal/login_link
2025-08-04T09:06:55+00:00 DEBUG #2989 - Loaded onboarding URL from cache: production-advanced_vaulting-express_checkout-
2025-08-04T09:06:55+00:00 INFO #2989 - Using cached onboarding URL for: production-advanced_vaulting-express_checkout-
2025-08-04T09:07:19+00:00 DEBUG #7719 - [New Request] GET /wp-json/wc/v3/wc_paypal/webhooks
2025-08-04T09:07:19+00:00 DEBUG #7719 - GET https://api.woocommerce.com/integrations/ppc/v1/notifications/webhooks
Response: Array
(
    [code] => 404
    [message] => Not Found
)

Response Body: {"statusCode":404,"error":"Not Found","message":"Not Found"}
2025-08-04T09:07:20+00:00 DEBUG #2884 - [New Request] POST /wp-json/wc/v3/wc_paypal/login_link
2025-08-04T09:07:20+00:00 DEBUG #2884 - Loaded onboarding URL from cache: production-advanced_vaulting-express_checkout-
2025-08-04T09:07:20+00:00 INFO #2884 - Using cached onboarding URL for: production-advanced_vaulting-express_checkout-
