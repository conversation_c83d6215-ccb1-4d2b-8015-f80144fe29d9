#!/bin/bash

# WordPress 初始化脚本

set -e

echo "开始初始化 WordPress..."

# 等待数据库连接
echo "等待数据库连接..."
while ! mysqladmin ping -h"$WORDPRESS_DB_HOST" --silent; do
    echo "等待数据库启动..."
    sleep 2
done

echo "数据库连接成功!"

# 安装 WP-CLI
if [ ! -f /usr/local/bin/wp ]; then
    echo "安装 WP-CLI..."
    curl -O https://raw.githubusercontent.com/wp-cli/wp-cli/v2.10.0/phar/wp-cli.phar
    chmod +x wp-cli.phar
    mv wp-cli.phar /usr/local/bin/wp
fi

# 切换到 WordPress 目录
cd /var/www/html

# 等待 WordPress 文件就绪
echo "等待 WordPress 文件就绪..."
while [ ! -f wp-config.php ]; do
    echo "等待 wp-config.php..."
    sleep 2
done

# 检查 WordPress 是否已安装
if ! wp core is-installed --allow-root 2>/dev/null; then
    echo "安装 WordPress..."
    wp core install \
        --url="http://localhost:8080" \
        --title="宠物用品商店" \
        --admin_user="admin" \
        --admin_password="admin123" \
        --admin_email="<EMAIL>" \
        --allow-root
    
    echo "WordPress 安装完成!"
else
    echo "WordPress 已安装，跳过安装步骤"
fi

# 安装和激活必要的插件
echo "安装必要的插件..."

# WooCommerce
if ! wp plugin is-installed woocommerce --allow-root; then
    wp plugin install woocommerce --activate --allow-root
fi

# WPGraphQL
if ! wp plugin is-installed wp-graphql --allow-root; then
    wp plugin install wp-graphql --activate --allow-root
fi

# WPGraphQL for WooCommerce
if ! wp plugin is-installed wp-graphql-woocommerce --allow-root; then
    wp plugin install wp-graphql-woocommerce --activate --allow-root
fi

# Advanced Custom Fields
if ! wp plugin is-installed advanced-custom-fields --allow-root; then
    wp plugin install advanced-custom-fields --activate --allow-root
fi

# Redis Object Cache
if ! wp plugin is-installed redis-cache --allow-root; then
    wp plugin install redis-cache --activate --allow-root
    wp redis enable --allow-root
fi

# JWT Authentication
if ! wp plugin is-installed jwt-authentication-for-wp-rest-api --allow-root; then
    wp plugin install jwt-authentication-for-wp-rest-api --activate --allow-root
fi

# CORS 支持
if ! wp plugin is-installed wp-cors --allow-root; then
    wp plugin install wp-cors --activate --allow-root
fi

# Stripe Payment Gateway
if ! wp plugin is-installed woocommerce-gateway-stripe --allow-root; then
    wp plugin install woocommerce-gateway-stripe --activate --allow-root
fi

# PayPal Payment Gateway
if ! wp plugin is-installed woocommerce-paypal-payments --allow-root; then
    wp plugin install woocommerce-paypal-payments --activate --allow-root
fi

# Multi-Currency Support
if ! wp plugin is-installed woocommerce-multilingual --allow-root; then
    wp plugin install woocommerce-multilingual --activate --allow-root
fi

# WPML (WordPress Multilingual)
if ! wp plugin is-installed sitepress-multilingual-cms --allow-root; then
    echo "WPML requires manual installation with license key"
fi

# Currency Switcher
if ! wp plugin is-installed woocommerce-currency-switcher --allow-root; then
    wp plugin install woocommerce-currency-switcher --activate --allow-root
fi

echo "插件安装完成!"

# 设置永久链接结构
echo "设置永久链接..."
wp rewrite structure '/%postname%/' --allow-root
wp rewrite flush --allow-root

# 创建基础页面
echo "创建基础页面..."
if ! wp post exists --post_type=page --post_title="首页" --allow-root; then
    wp post create --post_type=page --post_title="首页" --post_status=publish --allow-root
fi

if ! wp post exists --post_type=page --post_title="关于我们" --allow-root; then
    wp post create --post_type=page --post_title="关于我们" --post_status=publish --allow-root
fi

if ! wp post exists --post_type=page --post_title="联系我们" --allow-root; then
    wp post create --post_type=page --post_title="联系我们" --post_status=publish --allow-root
fi

# 设置 WooCommerce 基础配置
echo "配置 WooCommerce..."
wp option update woocommerce_store_address "123 Pet Street" --allow-root
wp option update woocommerce_store_city "New York" --allow-root
wp option update woocommerce_default_country "US:NY" --allow-root
wp option update woocommerce_store_postcode "10001" --allow-root
wp option update woocommerce_currency "USD" --allow-root
wp option update woocommerce_product_type "both" --allow-root
wp option update woocommerce_allow_tracking "no" --allow-root

# 配置多货币支持
echo "配置多货币支持..."
wp option update woocommerce_currency_pos "left" --allow-root
wp option update woocommerce_price_thousand_sep "," --allow-root
wp option update woocommerce_price_decimal_sep "." --allow-root
wp option update woocommerce_price_num_decimals "2" --allow-root

# 配置 Stripe
echo "配置 Stripe 支付..."
wp option update woocommerce_stripe_settings '{"enabled":"yes","title":"Credit Card","description":"Pay with your credit card via Stripe.","testmode":"yes","test_publishable_key":"'${STRIPE_PUBLISHABLE_KEY:-pk_test_example}'","test_secret_key":"'${STRIPE_SECRET_KEY:-sk_test_example}'","capture":"yes","payment_request":"yes","apple_pay":"yes","google_pay":"yes"}' --format=json --allow-root

# 配置 PayPal
echo "配置 PayPal 支付..."
wp option update woocommerce_ppcp-gateway_settings '{"enabled":"yes","title":"PayPal","description":"Pay via PayPal; you can pay with your credit card if you dont have a PayPal account.","client_id":"'${PAYPAL_CLIENT_ID:-example_client_id}'","client_secret":"'${PAYPAL_CLIENT_SECRET:-example_client_secret}'","sandbox_on":"yes"}' --format=json --allow-root

echo "WordPress 初始化完成!"

# 启动 Apache
exec "$@"
