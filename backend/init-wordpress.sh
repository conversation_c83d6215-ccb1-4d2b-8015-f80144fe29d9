#!/bin/bash

# WordPress 初始化脚本

set -e

echo "开始初始化 WordPress..."

# 等待数据库连接
echo "等待数据库连接..."
while ! php -r "
try {
    \$pdo = new PDO('mysql:host=${WORDPRESS_DB_HOST};port=3306', '${WORDPRESS_DB_USER}', '${WORDPRESS_DB_PASSWORD}');
    echo 'Connected successfully';
    exit(0);
} catch(PDOException \$e) {
    exit(1);
}
" 2>/dev/null; do
    echo "等待数据库启动..."
    sleep 2
done

echo "数据库连接成功!"

# 检查WordPress是否已经安装
if [ ! -f /var/www/html/index.php ]; then
    echo "WordPress 核心文件不存在，正在下载..."
    cd /var/www/html
    curl -O https://wordpress.org/latest.tar.gz
    tar -xzf latest.tar.gz --strip-components=1
    rm latest.tar.gz
    chown -R www-data:www-data /var/www/html
fi

echo "WordPress 已启动，请通过浏览器访问 http://localhost:8080 进行手动配置"



# 启动 Apache
exec "$@"
